[env:esp32s3_touch_lcd_169]
platform = espressif32
board = esp32-s3-devkitc-1 ; Using generic ESP32-S3 board (Waveshare ESP32-S3-Touch-LCD-1.69)
framework = arduino
monitor_speed = 115200

lib_deps =
    Wire
    lvgl/lvgl@^8.3.11
    bodmer/TFT_eSPI@^2.5.43
    moononournation/GFX Library for Arduino@^1.4.7 ; Arduino_GFX_Library for display support
    DFRobot/DFRobot_TCS3430 @ ^1.0.0 ; Check for latest version, ensure compatibility
    Preferences ; ESP32 Non-Volatile Storage library
    ; For CST816T touch, if the generic I2C in main.cpp is not sufficient:
    ; Example: adrianjx/CST816S @ ^1.0.2 (compatible with CST816T)

build_flags =
    -D LV_CONF_INCLUDE_SIMPLE  ; This allows lv_conf.h to be in src/ or include/
    -D LV_LVGL_H_INCLUDE_SIMPLE
    -I src                     ; Include src directory for lv_conf.h
    -D TFT_WIDTH=240
    -D TFT_HEIGHT=280
    ; -DBOARD_HAS_PSRAM          ; Disabled - PSRAM not detected
    ; -mfix-esp32-psram-cache-issue ; Disabled with PSRAM
    -DARDUINO_ESP32S3_DEV      ; ESP32-S3 specific defines
    ; TFT_eSPI Configuration for ESP32-S3-Touch-LCD-1.69
    -D ST7789_2_DRIVER         ; Use ST7789V2 driver
    -D TFT_MOSI=11             ; SDA (GPIO11)
    -D TFT_SCLK=10             ; SCL (GPIO10)
    -D TFT_CS=8                ; Chip Select (GPIO8)
    -D TFT_DC=2                ; Data/Command (GPIO2)
    -D TFT_RST=12              ; Reset (GPIO12)
    -D TFT_BL=16               ; Backlight (GPIO16, alternative pin)
    -D SPI_FREQUENCY=20000000  ; 20MHz SPI speed (reduced for stability)
    -D SPI_READ_FREQUENCY=20000000 ; 20MHz SPI read speed
    ; TFT_eSPI Font Configuration
    -D LOAD_GLCD               ; Font 1. Original Adafruit 8 pixel font needs ~1820 bytes in FLASH
    -D LOAD_FONT2              ; Font 2. Small 16 pixel high font, needs ~3534 bytes in FLASH
    -D LOAD_FONT4              ; Font 4. Medium 26 pixel high font, needs ~5848 bytes in FLASH
    -D LOAD_FONT6              ; Font 6. Large 48 pixel font, needs ~2666 bytes in FLASH
    -D LOAD_FONT7              ; Font 7. 7 segment 48 pixel font, needs ~2438 bytes in FLASH
    -D LOAD_FONT8              ; Font 8. Large 75 pixel font needs ~3256 bytes in FLASH
    -D LOAD_GFXFF              ; FreeFonts. Include access to the 48 Adafruit_GFX free fonts
    -D SMOOTH_FONT             ; Enable anti-aliased fonts

; If lv_conf.h is directly in the src directory, this line might not be strictly needed with LV_CONF_INCLUDE_SIMPLE
; but it's good practice if you have other includes in src/.
; -I src