<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32-S3 Precision Color Measurement</title>
    <link rel="stylesheet" href="style.css" />
</head>
<body>
    <div class="container">
        <h1>ESP32-S3 Precision Color Measurement</h1>
         
        <div class="control-buttons">
            <button id="led-button" class="off">Toggle LED</button>
            <button id="scan-button">Start Scan</button>
            <button id="save-button" style="display: none;">Save Sample</button>
            <button id="cancel-button" style="display: none;">Cancel</button>
            <button id="white-balance-button">White Calibration</button>
            <button id="black-calibration-button">Black Calibration</button>
            <button id="saved-samples-button">Saved Samples</button>
        </div>
        <p id="scan-status"></p>

        <div class="card">
            <div class="color-display">
                <div class="color-card">
                    <h2>Precision Color Measurement</h2>
                    <div id="measured-swatch" class="swatch"></div>
                    <div class="details">
                        <p id="measured-rgb">RGB: (..., ..., ...)</p>
                        <p id="measured-hex">Hex: #------</p>
                    </div>
                </div>
                <div class="color-card">
                    <h2>Captured Sample</h2>
                    <div id="frozen-swatch" class="swatch frozen-swatch"></div>
                    <div class="details">
                        <p id="frozen-rgb">RGB: (---, ---, ---)</p>
                        <p id="frozen-hex">Hex: #------</p>
                        <p id="frozen-status" class="frozen-status">Ready to scan</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="collapsible-header">Precision Measurement Data</div>
            <div class="collapsible-content">
                <div class="measurement-grid">
                    <div class="measurement-section">
                        <h3>Raw Tristimulus Values</h3>
                        <p>X: <span id="raw-x">--</span></p>
                        <p>Y: <span id="raw-y">--</span></p>
                        <p>Z: <span id="raw-z">--</span></p>
                    </div>
                    <div class="measurement-section">
                        <h3>Calibrated Values</h3>
                        <p>X: <span id="cal-x">--</span></p>
                        <p>Y: <span id="cal-y">--</span></p>
                        <p>Z: <span id="cal-z">--</span></p>
                    </div>
                    <div class="measurement-section">
                        <h3>Infrared Data</h3>
                        <p>IR1: <span id="ir1-value">--</span></p>
                        <p>IR2: <span id="ir2-value">--</span></p>
                    </div>
                    <div class="measurement-section">
                        <h3>Current Settings</h3>
                        <p>Gain: <span id="current-gain">--</span></p>
                        <p>Integration: <span id="current-integration">--</span></p>
                        <p>IR Comp: <span id="current-ir-comp">--</span></p>
                    </div>
                </div>
            </div>
        </div>



        <!-- New Advanced Settings Card -->
        <div class="card">
            <div class="collapsible-header">Advanced Settings</div>
            <div class="collapsible-content">
                <div class="tabs">
                    <div class="tab active" data-tab="sensor-settings">Sensor Settings</div>
                    <div class="tab" data-tab="color-preview">Live Preview</div>
                </div>
                 
                <div class="tab-content active" id="sensor-settings">
                    <form id="advanced-settings-form">
                        <div class="setting-group">
                            <h3>Sensor Configuration</h3>
                             
                            <div>
                                <label for="integration-time">Integration Time</label>
                                <select id="integration-time" name="integration_time">
                                    <option value="0x01">2.78ms (Fastest)</option>
                                    <option value="0x11">50ms (Balanced)</option>
                                    <option value="0x23">100ms (Accurate)</option>
                                    <option value="0x40">181ms (High Precision)</option>
                                    <option value="0xFF">711ms (Maximum)</option>
                                </select>
                                <div class="setting-description">
                                    Controls how long the sensor collects light. Longer times provide more accurate readings in low light but slower response.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Bright conditions: 2.78ms - 50ms</li>
                                        <li>Normal indoor lighting: 50ms - 100ms</li>
                                        <li>Low light conditions: 181ms - 711ms</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-gain">ALS Gain</label>
                                <select id="advanced-gain" name="gain">
                                    <option value="1">1x</option>
                                    <option value="4">4x</option>
                                    <option value="16">16x</option>
                                    <option value="64">64x</option>
                                </select>
                                <div class="setting-description">
                                    Amplifies the sensor signal. Higher gain improves sensitivity in low light but may cause saturation in bright conditions.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Direct sunlight: 1x</li>
                                        <li>Bright indoor lighting: 4x</li>
                                        <li>Normal indoor lighting: 16x</li>
                                        <li>Dim lighting: 64x</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                         
                        <div class="setting-group">
                            <h3>Color Processing</h3>
                             
                            <div>
                                <label for="advanced-ir-comp">IR Compensation Factor</label>
                                <input type="number" id="advanced-ir-comp" name="ir_comp" step="0.01" min="0" max="1" />
                                <div class="error-message" id="ir-comp-error">Value must be between 0 and 1</div>
                                <div class="setting-description">
                                    Reduces infrared light interference. Higher values remove more IR influence, improving color accuracy under artificial lighting.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Natural daylight: 0.01 - 0.03</li>
                                        <li>LED lighting: 0.02 - 0.05</li>
                                        <li>Fluorescent lighting: 0.03 - 0.07</li>
                                        <li>Incandescent lighting: 0.05 - 0.10</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-norm">sRGB Normalization Factor</label>
                                <input type="number" id="advanced-norm" name="norm" step="1000" min="1000" max="100000" />
                                <div class="error-message" id="norm-error">Value must be between 1,000 and 100,000</div>
                                <div class="setting-description">
                                    Scales raw sensor values to sRGB color space. Lower values make colors appear brighter, higher values make them darker.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Low light: 5,000 - 10,000</li>
                                        <li>Normal lighting: 10,000 - 20,000</li>
                                        <li>Bright lighting: 20,000 - 40,000</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="adaptive-scaling">Adaptive Scaling</label>
                                <select id="adaptive-scaling" name="adaptive_scaling">
                                    <option value="true">Enabled</option>
                                    <option value="false">Disabled</option>
                                </select>
                                <div class="setting-description">
                                    When enabled, automatically adjusts color scaling based on light intensity. Improves performance across varying lighting conditions.
                                </div>
                            </div>
                        </div>
                         
                        <div class="form-actions">
                            <p id="advanced-save-status"></p>
                            <button type="button" id="cancel-button">Cancel</button>
                            <button type="submit">Apply &amp; Save</button>
                        </div>
                    </form>
                </div>
                 
                <div class="tab-content" id="color-preview">
                    <h3>Live Settings Preview</h3>
                    <p>Adjust settings to see their effect on color detection in real-time.</p>
                     
                    <div class="preview-container">
                        <div class="preview-box">
                            <h4>Current Settings</h4>
                            <div id="current-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="current-rgb">--</span></p>
                                <p>XYZ: <span id="current-xyz">--</span></p>
                            </div>
                        </div>
                        <div class="preview-box">
                            <h4>New Settings</h4>
                            <div id="new-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="new-rgb">--</span></p>
                                <p>XYZ: <span id="new-xyz">--</span></p>
                            </div>
                        </div>
                    </div>
                     
                    <div class="nav-buttons">
                        <button type="button" id="back-to-settings">Back to Settings</button>
                        <button type="button" id="apply-preview">Apply These Settings</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Saved Samples Management Section -->
        <div class="card">
            <div class="collapsible-header">Saved Samples</div>
            <div class="collapsible-content">
                <div class="samples-header">
                    <h3>Color Sample Gallery</h3>
                    <p id="samples-count">Loading samples...</p>
                    <div class="samples-actions">
                        <button id="refresh-samples" class="btn btn-secondary">Refresh</button>
                        <button id="clear-all-samples" class="btn btn-danger">Clear All</button>
                    </div>
                </div>
                <div id="samples-grid" class="samples-grid">
                    <div class="loading-message">Loading saved samples...</div>
                </div>
            </div>
        </div>

        <footer>ESP32PROS3 &amp; TCS3430 - Scrofani</footer>
    </div>

    <!-- Sample Detail Modal -->
    <div id="sample-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Sample Details</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="sample-detail-grid">
                    <div class="sample-visual">
                        <div id="modal-swatch" class="large-swatch"></div>
                        <div class="sample-info">
                            <p><strong>Sample #<span id="modal-sample-number">--</span></strong></p>
                            <p id="modal-timestamp">--</p>
                        </div>
                    </div>
                    <div class="sample-data">
                        <h4>Color Information</h4>
                        <p>RGB: <span id="modal-rgb">--</span></p>
                        <p>Hex: <span id="modal-hex">--</span></p>
                        <p>Confidence: <span id="modal-confidence">--</span></p>

                        <h4>Dulux Paint Match</h4>
                        <p><strong><span id="modal-dulux-name">--</span></strong></p>
                        <p>Code: <span id="modal-dulux-code">--</span></p>
                        <p>LRV: <span id="modal-dulux-lrv">--</span></p>
                        <p>Delta E: <span id="modal-delta-e">--</span> (<span id="modal-match-quality">--</span>)</p>

                        <h4>Raw Sensor Data</h4>
                        <p>X: <span id="modal-raw-x">--</span></p>
                        <p>Y: <span id="modal-raw-y">--</span></p>
                        <p>Z: <span id="modal-raw-z">--</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const collapsibles = document.querySelectorAll('.collapsible-header');
            collapsibles.forEach(header => {
                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                    } else {
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            });



            const ledButton = document.getElementById('led-button');
            const scanButton = document.getElementById('scan-button');
            const scanStatus = document.getElementById('scan-status');
            const whiteBalanceButton = document.getElementById('white-balance-button');
            const blackCalibrationButton = document.getElementById('black-calibration-button');
            const saveButton = document.getElementById('save-button');
            const cancelButton = document.getElementById('cancel-button');
            const savedSamplesButton = document.getElementById('saved-samples-button');

            ledButton.addEventListener('click', () => {
                fetch('/toggle_led', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        updateLedButton(data.led_state);
                    })
                    .catch(error => console.error('Error toggling LED:', error));
            });

            scanButton.addEventListener('click', () => {
                const isScanning = scanButton.classList.contains('stop');
                if (!isScanning) {
                    scanStatus.textContent = 'Scanning...';
                    scanButton.textContent = 'Stop Scan';
                    scanButton.classList.add('stop');
                    fetch('/start_scan', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => console.log('Scan started:', text))
                        .catch(error => {
                            scanStatus.textContent = 'Error starting scan!';
                            scanButton.textContent = 'Start Scan';
                            scanButton.classList.remove('stop');
                            console.error('Error starting scan:', error);
                        });
                } else {
                    scanStatus.textContent = 'Scan stopped';
                    scanButton.textContent = 'Start Scan';
                    scanButton.classList.remove('stop');
                    fetch('/stop_scan', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => console.log('Scan stopped:', text))
                        .catch(error => {
                            scanStatus.textContent = 'Error stopping scan!';
                            console.error('Error stopping scan:', error);
                        });
                }
            });

            // Smart calibration function with object detection
            function performSmartCalibration(type, endpoint, button, expectedColor) {
                const originalText = button.textContent;
                button.disabled = true;

                // Step 1: Show instruction message
                button.textContent = `Place ${expectedColor} object near sensor...`;
                scanStatus.textContent = `Please place a ${expectedColor} reference object close to the sensor`;

                // Step 2: Wait for object detection
                let detectionAttempts = 0;
                const maxAttempts = 30; // 30 seconds timeout
                let baselineReading = null;

                const detectObject = () => {
                    fetch('/live_preview')
                        .then(response => response.json())
                        .then(data => {
                            detectionAttempts++;

                            // Get current light level (sum of XYZ)
                            const currentLevel = data.raw_x + data.raw_y + data.raw_z;

                            if (baselineReading === null) {
                                baselineReading = currentLevel;
                                setTimeout(detectObject, 1000);
                                return;
                            }

                            // Detect significant change in light level (object placed)
                            const changeThreshold = Math.max(100, baselineReading * 0.2);
                            const levelChange = Math.abs(currentLevel - baselineReading);

                            if (levelChange > changeThreshold || detectionAttempts >= maxAttempts) {
                                if (detectionAttempts >= maxAttempts) {
                                    scanStatus.textContent = 'Timeout - proceeding with calibration anyway';
                                } else {
                                    scanStatus.textContent = `${expectedColor} object detected! Calibrating...`;
                                }

                                button.textContent = 'Calibrating...';

                                // Step 3: Perform actual calibration
                                setTimeout(() => {
                                    fetch(endpoint, { method: 'POST' })
                                        .then(response => response.text())
                                        .then(text => {
                                            scanStatus.textContent = text;
                                            setTimeout(() => scanStatus.textContent = '', 5000);
                                        })
                                        .catch(error => {
                                            scanStatus.textContent = `${type} calibration failed!`;
                                            setTimeout(() => scanStatus.textContent = '', 5000);
                                            console.error(`Error during ${type} calibration:`, error);
                                        })
                                        .finally(() => {
                                            button.textContent = originalText;
                                            button.disabled = false;
                                        });
                                }, 1000);
                            } else {
                                // Continue monitoring
                                setTimeout(detectObject, 1000);
                            }
                        })
                        .catch(error => {
                            console.error('Error during object detection:', error);
                            setTimeout(detectObject, 1000);
                        });
                };

                // Start object detection
                setTimeout(detectObject, 500);
            }

            whiteBalanceButton.addEventListener('click', () => {
                performSmartCalibration('White balance', '/white_balance', whiteBalanceButton, 'white');
            });

            blackCalibrationButton.addEventListener('click', () => {
                performSmartCalibration('Black calibration', '/black_calibration', blackCalibrationButton, 'black');
            });

            function updateLedButton(state) {
                ledButton.textContent = `LED: ${state ? 'ON' : 'OFF'}`;
                ledButton.className = state ? 'on' : 'off';
            }

            let isFirstFetch = true;

            saveButton.addEventListener('click', () => {
                saveButton.disabled = true;
                saveButton.textContent = 'Saving...';
                fetch('/save_sample', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        scanStatus.textContent = 'Sample saved successfully!';
                        saveButton.style.display = 'none';
                        cancelButton.style.display = 'none';
                        loadSavedSamples(); // Refresh the samples list
                        setTimeout(() => {
                            scanStatus.textContent = '';
                        }, 3000);
                    })
                    .catch(error => {
                        console.error('Error saving sample:', error);
                        scanStatus.textContent = 'Error saving sample!';
                        saveButton.disabled = false;
                        saveButton.textContent = 'Save Sample';
                    });
            });

            cancelButton.addEventListener('click', () => {
                cancelButton.disabled = true;
                cancelButton.textContent = 'Cancelling...';
                fetch('/cancel_sample', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        scanStatus.textContent = 'Sample cancelled';
                        saveButton.style.display = 'none';
                        cancelButton.style.display = 'none';
                        setTimeout(() => {
                            scanStatus.textContent = '';
                        }, 3000);
                    })
                    .catch(error => {
                        console.error('Error cancelling sample:', error);
                        scanStatus.textContent = 'Error cancelling sample!';
                        cancelButton.disabled = false;
                        cancelButton.textContent = 'Cancel';
                    });
            });

            savedSamplesButton.addEventListener('click', () => {
                const samplesCard = document.querySelector('.card:has(.collapsible-header:contains("Saved Samples"))');
                if (samplesCard) {
                    const header = samplesCard.querySelector('.collapsible-header');
                    header.click(); // Toggle the collapsible section
                    loadSavedSamples(); // Refresh the samples
                }
            });

            function updateUI(data) {
                if (!data) return;

                if(data.data_ready){
                    // Update live color display
                    document.getElementById('measured-swatch').style.backgroundColor = `rgb(${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                    document.getElementById('measured-rgb').innerText = `RGB: (${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                    document.getElementById('measured-hex').innerText = `Hex: ${data.hex_color}`;

                    // Update frozen color display
                    const frozenSwatch = document.getElementById('frozen-swatch');
                    const frozenRgb = document.getElementById('frozen-rgb');
                    const frozenHex = document.getElementById('frozen-hex');
                    const frozenStatus = document.getElementById('frozen-status');

                    if (data.has_frozen_color) {
                        frozenSwatch.style.backgroundColor = `rgb(${data.frozen_r}, ${data.frozen_g}, ${data.frozen_b})`;
                        frozenRgb.innerText = `RGB: (${data.frozen_r}, ${data.frozen_g}, ${data.frozen_b})`;
                        frozenHex.innerText = `Hex: ${data.frozen_hex}`;
                        frozenStatus.innerText = `Confidence: ${data.frozen_confidence.toFixed(1)}%`;
                        frozenSwatch.classList.add('has-color');
                    } else {
                        frozenSwatch.style.backgroundColor = '#f0f0f0';
                        frozenRgb.innerText = 'RGB: (---, ---, ---)';
                        frozenHex.innerText = 'Hex: #------';
                        frozenStatus.innerText = data.is_scanning ? 'Scanning...' : 'Ready to scan';
                        frozenSwatch.classList.remove('has-color');
                    }

                    // Update scan status and buttons
                    if (data.is_scanning) {
                        // Currently scanning
                        scanStatus.textContent = 'Scanning...';
                        scanButton.textContent = 'Stop Scan';
                        scanButton.classList.add('stop');
                        saveButton.style.display = 'none';
                        cancelButton.style.display = 'none';
                    } else {
                        // Not scanning
                        scanButton.textContent = 'Start Scan';
                        scanButton.classList.remove('stop');

                        // Show save and cancel buttons if we have frozen color data
                        if (data.has_frozen_color) {
                            scanStatus.textContent = 'Scan complete - Ready to save';
                            saveButton.style.display = 'inline-block';
                            saveButton.disabled = false;
                            saveButton.textContent = 'Save Sample';
                            cancelButton.style.display = 'inline-block';
                            cancelButton.disabled = false;
                            cancelButton.textContent = 'Cancel';
                        } else {
                            scanStatus.textContent = 'Ready to scan';
                            saveButton.style.display = 'none';
                            cancelButton.style.display = 'none';
                        }
                    }
                    updateLedButton(data.led_state);
                }
                
                if (isFirstFetch) {
                    updateLedButton(data.led_state);
                    isFirstFetch = false;
                }
            }

            function fetchData() {
                fetch('/fulldata')
                    .then(response => response.json())
                    .then(data => updateUI(data))
                    .catch(error => console.error('Error fetching data:', error));
            }

            // Saved Samples Management Functions
            function loadSavedSamples() {
                const samplesGrid = document.getElementById('samples-grid');
                const samplesCount = document.getElementById('samples-count');

                samplesGrid.innerHTML = '<div class="loading-message">Loading saved samples...</div>';

                fetch('/get_samples')
                    .then(response => response.json())
                    .then(data => {
                        displaySavedSamples(data);
                        samplesCount.textContent = `Showing ${data.samples.length} of ${data.max_samples} samples`;
                    })
                    .catch(error => {
                        console.error('Error loading samples:', error);
                        samplesGrid.innerHTML = '<div class="error-message">Error loading samples. Please try again.</div>';
                        samplesCount.textContent = 'Error loading samples';
                    });
            }

            function displaySavedSamples(data) {
                const samplesGrid = document.getElementById('samples-grid');

                if (!data.samples || data.samples.length === 0) {
                    samplesGrid.innerHTML = `
                        <div class="empty-state">
                            <h4>No samples saved yet</h4>
                            <p>Complete a scan and click 'Save Sample' to get started.</p>
                        </div>
                    `;
                    return;
                }

                const samplesHTML = data.samples.map((sample, index) => {
                    const timestamp = new Date(sample.timestamp).toLocaleString();
                    const matchQuality = sample.delta_e <= 5 ? 'Excellent' :
                                       sample.delta_e <= 10 ? 'Good' :
                                       sample.delta_e <= 20 ? 'Fair' : 'Poor';

                    // Check if sample needs color matching
                    const needsMatching = !sample.dulux_name || sample.dulux_name === '' || sample.delta_e <= 0 || sample.delta_e > 100;
                    const isProcessing = needsMatching && sample.dulux_name !== 'No match found';

                    let duluxDisplay = '';
                    if (isProcessing) {
                        duluxDisplay = `
                            <p class="sample-dulux processing">
                                <span class="loading-spinner">⟳</span> Processing color match...
                            </p>
                            <p class="sample-code">Analyzing paint database...</p>
                            <p class="sample-delta">Please wait...</p>
                        `;
                    } else if (sample.dulux_name && sample.dulux_name !== '' && sample.delta_e > 0 && sample.delta_e <= 100) {
                        duluxDisplay = `
                            <p class="sample-dulux"><strong>${sample.dulux_name}</strong></p>
                            <p class="sample-code">Code: ${sample.dulux_code}, LRV: ${sample.dulux_lrv}</p>
                            <p class="sample-delta">ΔE: ${sample.delta_e.toFixed(1)} (${matchQuality})</p>
                        `;
                    } else {
                        duluxDisplay = `
                            <p class="sample-dulux no-match">No match found</p>
                            <p class="sample-code">Color outside paint range</p>
                            <p class="sample-delta">ΔE: > 50 (Poor match)</p>
                        `;
                    }

                    return `
                        <div class="sample-card ${isProcessing ? 'processing' : ''}" onclick="showSampleDetail(${JSON.stringify(sample).replace(/"/g, '&quot;')})">
                            <button class="delete-button" onclick="event.stopPropagation(); deleteSample(${index}, ${sample.sample_number})" title="Delete Sample">×</button>
                            <div class="sample-swatch" style="background-color: rgb(${sample.r}, ${sample.g}, ${sample.b})"></div>
                            <div class="sample-info">
                                <h4>Sample #${sample.sample_number}</h4>
                                <p class="sample-timestamp">${timestamp}</p>
                                <p class="sample-rgb">RGB: (${sample.r}, ${sample.g}, ${sample.b})</p>
                                <p class="sample-hex">${sample.hex}</p>
                                ${duluxDisplay}
                            </div>
                        </div>
                    `;
                }).join('');

                samplesGrid.innerHTML = samplesHTML;
            }

            function showSampleDetail(sample) {
                const modal = document.getElementById('sample-modal');
                const timestamp = new Date(sample.timestamp).toLocaleString();
                const matchQuality = sample.delta_e <= 5 ? 'Excellent' :
                                   sample.delta_e <= 10 ? 'Good' :
                                   sample.delta_e <= 20 ? 'Fair' : 'Poor';

                // Populate modal with sample data
                document.getElementById('modal-swatch').style.backgroundColor = `rgb(${sample.r}, ${sample.g}, ${sample.b})`;
                document.getElementById('modal-sample-number').textContent = sample.sample_number;
                document.getElementById('modal-timestamp').textContent = timestamp;
                document.getElementById('modal-rgb').textContent = `(${sample.r}, ${sample.g}, ${sample.b})`;
                document.getElementById('modal-hex').textContent = sample.hex;
                document.getElementById('modal-confidence').textContent = `${sample.confidence.toFixed(1)}%`;
                document.getElementById('modal-dulux-name').textContent = sample.dulux_name;
                document.getElementById('modal-dulux-code').textContent = sample.dulux_code;
                document.getElementById('modal-dulux-lrv').textContent = sample.dulux_lrv;
                document.getElementById('modal-delta-e').textContent = sample.delta_e.toFixed(2);
                document.getElementById('modal-match-quality').textContent = matchQuality;
                document.getElementById('modal-raw-x').textContent = sample.raw_x;
                document.getElementById('modal-raw-y').textContent = sample.raw_y;
                document.getElementById('modal-raw-z').textContent = sample.raw_z;

                modal.style.display = 'block';
            }

            // Modal close functionality
            document.querySelector('.close').addEventListener('click', () => {
                document.getElementById('sample-modal').style.display = 'none';
            });

            window.addEventListener('click', (event) => {
                const modal = document.getElementById('sample-modal');
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });

            // Individual sample deletion function
            function deleteSample(index, sampleNumber) {
                if (confirm(`Delete Sample #${sampleNumber}? This action cannot be undone.`)) {
                    fetch('/delete_sample', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `index=${index}`
                    })
                    .then(response => response.text())
                    .then(text => {
                        if (text === 'Sample deleted') {
                            scanStatus.textContent = `Sample #${sampleNumber} deleted successfully`;
                            loadSavedSamples(); // Refresh the samples list
                            setTimeout(() => scanStatus.textContent = '', 3000);
                        } else {
                            throw new Error(text);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting sample:', error);
                        scanStatus.textContent = `Error deleting sample: ${error.message}`;
                        setTimeout(() => scanStatus.textContent = '', 5000);
                    });
                }
            }

            // Make deleteSample available globally
            window.deleteSample = deleteSample;

            // Enhanced sample loading with automatic refresh for processing samples
            let processingCheckInterval = null;

            function loadSavedSamples() {
                const samplesGrid = document.getElementById('samples-grid');
                const samplesCount = document.getElementById('samples-count');

                samplesGrid.innerHTML = '<div class="loading-message">Loading saved samples...</div>';

                fetch('/get_samples')
                    .then(response => response.json())
                    .then(data => {
                        displaySavedSamples(data);
                        samplesCount.textContent = `Showing ${data.samples.length} of ${data.max_samples} samples`;

                        // CRITICAL FIX: Check if any samples need processing AND if color database is available
                        // Only enable periodic refresh if database is loaded and samples actually need processing
                        const hasProcessingSamples = data.samples.some(sample =>
                            !sample.dulux_name || sample.dulux_name === '' ||
                            (sample.delta_e <= 0 || sample.delta_e > 100) && sample.dulux_name !== 'No match found'
                        );

                        // CRITICAL FIX: Only set up periodic refresh if database is available
                        // Check if we have any samples with actual Dulux data (indicating database is loaded)
                        const hasDuluxData = data.samples.some(sample =>
                            sample.dulux_name && sample.dulux_name !== '' &&
                            sample.dulux_name !== 'No match found' && sample.delta_e > 0 && sample.delta_e <= 100
                        );

                        if (hasProcessingSamples && hasDuluxData) {
                            // Only set up periodic refresh if database is loaded and samples need processing
                            console.log('Setting up periodic refresh: samples need processing and database is loaded');
                            if (processingCheckInterval) {
                                clearInterval(processingCheckInterval);
                            }
                            processingCheckInterval = setInterval(() => {
                                console.log('Periodic refresh: checking for sample updates...');
                                loadSavedSamples();
                            }, 10000); // FIXED: Increased to 10 seconds to reduce load
                        } else {
                            // Clear interval if no samples are processing OR database is not loaded
                            if (processingCheckInterval) {
                                console.log('Clearing periodic refresh: no processing needed or database not loaded');
                                clearInterval(processingCheckInterval);
                                processingCheckInterval = null;
                            }
                            if (!hasDuluxData) {
                                console.log('Color database not loaded - periodic refresh disabled');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error loading samples:', error);
                        samplesGrid.innerHTML = '<div class="error-message">Error loading samples. Please try again.</div>';
                        samplesCount.textContent = 'Error loading samples';
                    });
            }

            // Additional sample management buttons
            document.getElementById('refresh-samples').addEventListener('click', loadSavedSamples);

            document.getElementById('clear-all-samples').addEventListener('click', () => {
                if (confirm('Are you sure you want to delete all saved samples? This action cannot be undone.')) {
                    fetch('/clear_samples', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => {
                            loadSavedSamples();
                            scanStatus.textContent = 'All samples cleared';
                            setTimeout(() => scanStatus.textContent = '', 3000);
                        })
                        .catch(error => {
                            console.error('Error clearing samples:', error);
                            scanStatus.textContent = 'Error clearing samples';
                        });
                }
            });

            // CRITICAL FIX: Clear any existing intervals on page load to prevent infinite loops
            if (processingCheckInterval) {
                clearInterval(processingCheckInterval);
                processingCheckInterval = null;
                console.log('Cleared existing processing check interval on page load');
            }

            // Load samples on page load
            loadSavedSamples();

            fetchData();
            setInterval(fetchData, 1500);
        });
    </script>
</body>
</html>
