[env:esp32-s3-devkitc-1]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

# Serial monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

# Build settings
build_flags =
    -D TFT_WIDTH=240
    -D TFT_HEIGHT=280
    -DARDUINO_ESP32S3_DEV

# Libraries
lib_deps =
    moononournation/GFX Library for Arduino@^1.4.7

# Upload settings
upload_speed = 921600
