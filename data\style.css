body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif; background-color: #f4f7f6; color: #333; margin: 0; padding: 20px; }
.container { max-width: 800px; margin: auto; }
h1 { color: #1a237e; text-align: center; }
.card { background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); padding: 25px; margin-bottom: 20px; }
.color-display { display: flex; justify-content: space-around; align-items: flex-start; flex-wrap: wrap; gap: 20px; }
.color-card { flex: 1; min-width: 250px; text-align: center; }
.color-card h2 { margin-top: 0; color: #3949ab; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; }
.swatch { width: 100%; height: 120px; border-radius: 6px; border: 1px solid #ccc; background-color: #f0f0f0; margin-bottom: 15px; transition: background-color 0.5s ease; }
.details p { margin: 8px 0; font-size: 1.1em; }
.stats { margin-top: 15px; }
.stats p { font-size: 1.2em; font-weight: bold; text-align: center; margin: 5px 0; }
#confidence.high { color: #2e7d32; }
#confidence.medium { color: #f57f17; }
#confidence.low { color: #c62828; }
.collapsible-header { cursor: pointer; padding: 12px; background-color: #e8eaf6; border-radius: 8px; font-weight: bold; color: #1a237e; margin-top: 10px; user-select: none; }
.collapsible-header::after { content: '\25BC'; float: right; transition: transform 0.2s; }
.collapsible-header.active::after { transform: rotate(180deg); }
.collapsible-content { padding: 0 15px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background-color: #fafafa; border-radius: 0 0 8px 8px; }
.live-data-grid, .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; padding: 15px 0; }
label { font-weight: bold; margin-bottom: 5px; display: block; }
input, select { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
.form-actions { display: flex; justify-content: flex-end; align-items: center; margin-top: 15px; gap: 15px; }
button { background-color: #3949ab; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 1em; }
button:hover { background-color: #1a237e; }
#save-status { text-align: right; color: green; font-weight: bold; height: 1em; }
.control-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
#led-button.on { background-color: #2e7d32; }
#led-button.off { background-color: #c62828; }
#scan-button.stop { background-color: #c62828; }
#white-balance-button { background-color: #f57c00; }
#white-balance-button:hover { background-color: #ef6c00; }
#black-calibration-button { background-color: #424242; }
#black-calibration-button:hover { background-color: #212121; }
#scan-status { text-align: center; color: #3949ab; font-weight: bold; margin-top: 10px; }
footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
.setting-description { font-size: 0.9em; color: #666; margin-top: 3px; margin-bottom: 10px; }
.setting-group { border-left: 3px solid #3949ab; padding-left: 15px; margin-bottom: 20px; }
.setting-group h3 { color: #3949ab; margin-top: 0; }
.preview-container { display: flex; gap: 20px; margin-top: 15px; }
.preview-box { flex: 1; text-align: center; }
.preview-swatch { width: 100%; height: 80px; border-radius: 6px; border: 1px solid #ccc; margin-bottom: 10px; }
.preview-data { font-size: 0.9em; }
.tabs { display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd; }
.tab { padding: 10px 20px; cursor: pointer; border-radius: 5px 5px 0 0; }
.tab.active { background-color: #e8eaf6; border: 1px solid #ddd; border-bottom: none; font-weight: bold; }
.tab-content { display: none; }
.tab-content.active { display: block; }
.recommended-values { background-color: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 0.9em; }
.recommended-values h4 { margin-top: 0; margin-bottom: 5px; color: #3949ab; }
.recommended-values ul { margin: 0; padding-left: 20px; }
.error-message { color: #c62828; font-size: 0.9em; margin-top: 5px; display: none; }
.nav-buttons { display: flex; justify-content: space-between; margin-top: 20px; }
.nav-buttons button { background-color: #757575; }
.nav-buttons button:hover { background-color: #616161; }

/* Quality indicators styling */
.quality-indicators { padding: 15px; }
.quality-item { display: flex; justify-content: space-between; margin-bottom: 8px; }
.quality-label { font-weight: bold; color: #555; }
.quality-value { font-weight: bold; }
.quality-value.excellent { color: #2e7d32; }
.quality-value.good { color: #388e3c; }
.quality-value.fair { color: #f57c00; }
.quality-value.poor { color: #c62828; }
.quality-value.calibrated { color: #2e7d32; }
.quality-value.not-calibrated { color: #c62828; }
.quality-value.saturated { color: #c62828; }
.quality-value.normal { color: #2e7d32; }

/* Frozen color display styling */
.frozen-swatch {
    background-color: #f0f0f0;
    border: 2px dashed #ccc;
    transition: all 0.5s ease;
}
.frozen-swatch.has-color {
    border: 2px solid #3949ab;
    box-shadow: 0 2px 8px rgba(57, 73, 171, 0.2);
}
.frozen-status {
    font-size: 0.9em;
    color: #666;
    font-style: italic;
}

/* Saved Samples Management Styling */
.samples-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.samples-header h3 {
    margin: 0;
    color: #333;
}

.samples-actions {
    display: flex;
    gap: 10px;
}

.samples-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.samples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.sample-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.sample-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #3949ab;
}

.sample-card.processing {
    border-color: #ff9800;
    background: linear-gradient(135deg, #fff 0%, #fff8e1 100%);
}

.sample-card.processing:hover {
    border-color: #f57c00;
}

/* Delete button styling */
.delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    line-height: 1;
    padding: 0;
}

.delete-button:hover {
    background: #c82333;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-button:active {
    transform: scale(0.95);
}

.sample-swatch {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 2px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sample-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
}

.sample-timestamp {
    font-size: 12px;
    color: #666;
    margin: 0 0 8px 0;
}

.sample-rgb, .sample-hex {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    margin: 3px 0;
    color: #444;
}

.sample-dulux {
    margin: 8px 0 3px 0;
    color: #2e7d32;
    font-size: 14px;
}

.sample-code {
    font-size: 12px;
    color: #666;
    margin: 0 0 5px 0;
}

.sample-delta {
    font-size: 12px;
    color: #1976d2;
    margin: 5px 0 0 0;
    font-weight: 500;
}

/* Processing and loading indicators */
.sample-dulux.processing {
    color: #ff9800;
    font-style: italic;
}

.sample-dulux.no-match {
    color: #666;
    font-style: italic;
}

.loading-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
    margin-right: 5px;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced visual feedback for processing samples */
.sample-card.processing .sample-swatch {
    border-color: #ff9800;
    box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.2);
}

.sample-card.processing .sample-info {
    opacity: 0.8;
}

.loading-message, .error-message {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.error-message {
    color: #dc3545;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-state h4 {
    margin: 0 0 10px 0;
    color: #333;
}

/* Modal Styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    line-height: 1;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.sample-detail-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
}

.sample-visual {
    text-align: center;
}

.large-swatch {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    margin: 0 auto 15px;
    border: 3px solid #ddd;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sample-info p {
    margin: 5px 0;
    text-align: center;
}

.sample-data h4 {
    margin: 0 0 10px 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.sample-data p {
    margin: 5px 0;
    font-size: 14px;
}

.sample-data strong {
    color: #2e7d32;
}

/* Responsive Design for Samples */
@media (max-width: 768px) {
    .samples-grid {
        grid-template-columns: 1fr;
    }

    .samples-header {
        flex-direction: column;
        align-items: stretch;
    }

    .samples-actions {
        justify-content: center;
    }

    .sample-detail-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    /* Enhanced delete button for mobile */
    .delete-button {
        width: 28px;
        height: 28px;
        font-size: 18px;
        top: 10px;
        right: 10px;
    }

    /* Larger touch targets for mobile */
    .sample-card {
        padding: 18px;
        margin-bottom: 10px;
    }

    .sample-swatch {
        width: 70px;
        height: 70px;
    }
}
