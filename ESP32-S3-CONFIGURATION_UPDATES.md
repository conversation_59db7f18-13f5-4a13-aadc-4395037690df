# ESP32-S3-Touch-LCD-1.69 Configuration Updates

## Summary of Changes Made

Based on the user's information about the ESP32-S3-Touch-LCD-1.69 board configuration, the following updates were made to fix pin configuration inconsistencies and optimize the setup:

## 1. Pin Configuration Fixes

### Updated `src/config.h`:
- **Backlight Pin Comment**: Added note about trying GPIO16 if GPIO4 doesn't work for backlight
- **Touch Controller**: Updated from CST816S to CST816T (correct chip)
- **I2C Bus Comment**: Corrected I2C pins reference from GPIO10/11 to GPIO6/7
- **Sensor Interrupt Pin**: Changed from GPIO2 to GPIO17 (GPIO2 is used by TFT_DC)

### Current Pin Configuration (Confirmed):
```cpp
// Display SPI (ST7789V2)
#define TFT_MOSI_PIN       11      // GPIO11 - SDA (MOSI)
#define TFT_SCLK_PIN       10      // GPIO10 - SCL (Clock)
#define TFT_CS_PIN         8       // GPIO8 - CSX (Chip Select)
#define TFT_DC_PIN         2       // GPIO2 - D/CX (Data/Command)
#define TFT_RST_PIN        12      // GPIO12 - RESX (Reset)
#define TFT_BL_PIN         4       // GPIO4 - Backlight (try GPIO16 if issues)

// I2C Bus (Shared)
#define I2C_SDA_PIN        6       // GPIO6 - SDA
#define I2C_SCL_PIN        7       // GPIO7 - SCL

// Touch Controller (CST816T)
#define TOUCH_I2C_ADDRESS  0x15    // CST816T I2C address
#define TOUCH_INT_PIN      9       // GPIO9 - Touch interrupt
#define TOUCH_RST_PIN      13      // GPIO13 - Touch reset

// External Sensor
#define SENSOR_INTERRUPT_PIN 17    // GPIO17 - TCS3430 interrupt (moved from GPIO2)
```

## 2. PlatformIO Configuration Cleanup

### Updated `platformio.ini`:
- **Removed Duplicate Build Flags**: Eliminated duplicate TFT_eSPI configuration entries
- **SPI Speed**: Set to 27MHz (conservative for stability, can increase to 40MHz if no flickering)
- **Touch Controller Comment**: Updated to reference CST816T instead of CST816S
- **Organized Comments**: Better organization of font and display configuration sections

### Key Build Flags:
```ini
-D ST7789_2_DRIVER         ; Use ST7789V2 driver
-D SPI_FREQUENCY=27000000  ; 27MHz SPI speed (conservative, reduce from 40MHz if flickering)
-D SPI_READ_FREQUENCY=20000000 ; 20MHz SPI read speed
```

## 3. LVGL Configuration Enhancements

### Updated `src/lv_conf.h`:
- **Added Touch Input Support**: Enabled proper touch input configuration for CST816T
- **Input Device Settings**: Added comprehensive touch input parameters

### New Touch Configuration:
```cpp
// Enable touch input support for CST816T
#define LV_USE_INDEV_POINTER 1
#define LV_INDEV_DEF_READ_PERIOD 30    // Input device read period in milliseconds
#define LV_INDEV_DEF_DRAG_LIMIT  10    // Drag threshold in pixels
#define LV_INDEV_DEF_DRAG_THROW  10    // Drag throw slow-down in [%]
#define LV_INDEV_DEF_LONG_PRESS_TIME 400 // Long press time in milliseconds
```

## 4. Main Application Updates

### Updated `src/main.cpp`:
- **Display Initialization**: Updated log message to show correct pin numbers
- **SPI Frequency**: Changed from 40MHz to 27MHz for stability
- **Touch Input**: Re-enabled touch input (was temporarily disabled)
- **Sensor Interrupt**: Updated log message for new GPIO17 pin

### Key Changes:
```cpp
// Display initialization with correct pins
log_info("Display", "Initializing display with pins: MOSI=11, SCK=10, CS=8, DC=2, RST=12, BL=4");

// Conservative SPI frequency
bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1, TFT_SPI_HOST, 27000000);

// Touch input enabled
log_info("LVGL", "CST816T touch input enabled and registered.");
```

## 5. Technical Specifications Confirmed

### ST7789V2 Display:
- **Resolution**: 240×280 pixels
- **SPI Speed**: 27MHz (conservative), up to 40MHz supported
- **Color Format**: RGB565 (16-bit)
- **Rotation**: Use `tft.setRotation(0-3)` for orientation adjustment

### CST816T Touch Controller:
- **Interface**: I2C (shared bus with other peripherals)
- **Address**: 0x15 (7-bit)
- **Features**: Single touch point, gesture recognition
- **Separate I2C pins**: Uses GPIO6/7, not the display SPI pins

## 6. Troubleshooting Notes

### If Display Issues Occur:
1. **Flickering**: Reduce SPI_FREQUENCY from 27MHz to 20MHz
2. **Backlight**: Try GPIO16 if GPIO4 doesn't control backlight
3. **Colors Inverted**: Check LV_COLOR_16_SWAP setting in lv_conf.h
4. **Orientation**: Adjust rotation with `tft.setRotation(0-3)`

### If Touch Issues Occur:
1. **No Touch Response**: Verify I2C connections on GPIO6/7
2. **Incorrect Coordinates**: Check touch coordinate mapping in touchpad_read_callback
3. **Touch Sensitivity**: Adjust LV_INDEV_DEF_DRAG_LIMIT in lv_conf.h

## 7. Next Steps

1. **Test the Configuration**: Build and upload the firmware to verify all changes work correctly
2. **SPI Speed Optimization**: If display works well at 27MHz, try increasing to 40MHz for better performance
3. **Touch Calibration**: Test touch input and adjust sensitivity if needed
4. **Color Accuracy**: Verify color conversion and IR compensation with the updated configuration

## 8. Files Modified

- `src/config.h` - Pin definitions and touch controller updates
- `platformio.ini` - Build flags cleanup and SPI speed optimization
- `src/lv_conf.h` - Touch input support configuration
- `src/main.cpp` - Display initialization and touch input enablement

All changes maintain backward compatibility while fixing the pin configuration inconsistencies and optimizing for the ESP32-S3-Touch-LCD-1.69 board.
