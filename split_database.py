#!/usr/bin/env python3
"""
Split the large Dulux color database into 4 smaller JSON files
to reduce memory usage during ESP32 color matching operations.
"""

import json
import os
import math

def split_dulux_database():
    """Split dulux_minified.json into 4 smaller files"""
    
    # Read the original database
    input_file = "data/dulux_minified.json"
    
    print(f"Reading {input_file}...")
    with open(input_file, 'r', encoding='utf-8') as f:
        colors = json.load(f)
    
    total_colors = len(colors)
    colors_per_file = math.ceil(total_colors / 4)
    
    print(f"Total colors: {total_colors}")
    print(f"Colors per file: {colors_per_file}")
    
    # Split into 4 parts
    for i in range(4):
        start_idx = i * colors_per_file
        end_idx = min((i + 1) * colors_per_file, total_colors)
        
        part_colors = colors[start_idx:end_idx]
        part_filename = f"data/dulux_part{i+1}.json"
        
        print(f"Creating {part_filename} with {len(part_colors)} colors (indices {start_idx}-{end_idx-1})")
        
        # Write the part file (minified)
        with open(part_filename, 'w', encoding='utf-8') as f:
            json.dump(part_colors, f, separators=(',', ':'))
        
        # Check file size
        file_size = os.path.getsize(part_filename)
        print(f"  File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    
    # Verify total
    total_split_colors = 0
    total_split_size = 0
    
    for i in range(4):
        part_filename = f"data/dulux_part{i+1}.json"
        with open(part_filename, 'r', encoding='utf-8') as f:
            part_colors = json.load(f)
            total_split_colors += len(part_colors)
            total_split_size += os.path.getsize(part_filename)
    
    print(f"\nVerification:")
    print(f"Original colors: {total_colors}")
    print(f"Split colors: {total_split_colors}")
    print(f"Original size: {os.path.getsize(input_file):,} bytes")
    print(f"Split total size: {total_split_size:,} bytes")
    print(f"Match: {total_colors == total_split_colors}")
    
    # Show sample from each file
    print(f"\nSample colors from each file:")
    for i in range(4):
        part_filename = f"data/dulux_part{i+1}.json"
        with open(part_filename, 'r', encoding='utf-8') as f:
            part_colors = json.load(f)
            if part_colors:
                sample = part_colors[0]
                print(f"  Part {i+1}: {sample['name']} (Code: {sample['code']}, RGB: {sample['r']},{sample['g']},{sample['b']})")

if __name__ == "__main__":
    split_dulux_database()
    print("\nDatabase split complete!")
