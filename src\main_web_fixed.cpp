/*
 * ESP32-S3 Color Measurement System with Split Database and Loop Prevention
 * Fixed version to prevent infinite background color matching loops
 */

#include <WiFi.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <Wire.h>
#include <Adafruit_NeoPixel.h>
#include "DFRobot_TCS3430.h"

// Hardware Configuration
#define RGB_LED_PIN 18
#define RGB_LED_COUNT 1
#define BUZZER_PIN 42
#define TCS3430_INT_PIN 39

// WiFi Configuration
const char* ssid = "Wifi 6";
const char* password = "Scrofani1985";

// Web Server
WebServer server(80);

// RGB LED
Adafruit_NeoPixel strip(RGB_LED_COUNT, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);

// Color Sensor
DFRobot_TCS3430 tcs3430;

// Preferences for EEPROM storage
Preferences preferences;

// Color matching structures
struct DuluxColorMatch {
    bool isValid;
    char name[64];
    char code[16];
    char lrv[8];
    char id[16];
    uint8_t r, g, b;
    float deltaE;
};

struct ColorSample {
    bool isValid;
    int sampleNumber;
    uint8_t r, g, b;
    char hex[8];
    char duluxName[64];
    char duluxCode[16];
    char duluxLRV[8];
    char duluxID[16];
    uint8_t duluxR, duluxG, duluxB;
    float deltaE;
    uint16_t rawX, rawY, rawZ;
    float confidence;
    unsigned long timestamp;
};

// Sample storage
const int MAX_STORED_SAMPLES = 30;
ColorSample storedSamples[MAX_STORED_SAMPLES];
int totalSamples = 0;
int currentSampleIndex = 0;

// Split database configuration
const char* DATABASE_FILES[] = {
    "/dulux_part1.json",
    "/dulux_part2.json", 
    "/dulux_part3.json",
    "/dulux_part4.json"
};
const int NUM_DATABASE_FILES = 4;
const size_t MAX_FILE_BUFFER_SIZE = 130000;
bool colorDatabaseLoaded = false;

// Background color matching prevention
static bool isBackgroundMatchingActive = false;
static unsigned long lastBackgroundMatchingTime = 0;
const unsigned long BACKGROUND_MATCHING_INTERVAL = 30000; // 30 seconds

// Current measurement state
struct {
    bool dataReady;
    uint8_t measuredR, measuredG, measuredB;
    char hexColor[8];
    uint16_t rawX, rawY, rawZ;
    uint16_t ir1, ir2;
    bool isScanning;
    bool ledState;
    bool hasFrozenColor;
    uint8_t frozenR, frozenG, frozenB;
    char frozenHex[8];
    float frozenConfidence;
    DuluxColorMatch currentMatch;
} measurementState = {0};

// Function declarations
void setupWiFi();
void setupWebServer();
void setupSensor();
void setupFileSystem();
bool loadSplitColorDatabase();
DuluxColorMatch searchColorDatabaseFile(const char* filename, uint8_t targetR, uint8_t targetG, uint8_t targetB);
DuluxColorMatch findBestColorMatch(uint8_t targetR, uint8_t targetG, uint8_t targetB);
void performBackgroundColorMatching();
void loadSamplesFromEEPROM();
void saveSamplesToEEPROM();
float calculateDeltaE(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2);
void handleRoot();
void handleFullData();
void handleGetSamples();
void handleDeleteSample();
void handleClearSamples();
void handleSaveSample();
void handleStartScan();
void handleStopScan();
void handleToggleLED();
void handleCancel();

void setup() {
    Serial.begin(115200);
    Serial.println("ESP32-S3 Color Measurement System Starting...");
    
    // Initialize hardware
    strip.begin();
    strip.show();
    
    pinMode(BUZZER_PIN, OUTPUT);
    
    // Initialize file system
    setupFileSystem();
    
    // Initialize WiFi
    setupWiFi();
    
    // Initialize sensor
    setupSensor();
    
    // Load color database
    if (loadSplitColorDatabase()) {
        Serial.println("Split color database system ready!");
        colorDatabaseLoaded = true;
    } else {
        Serial.println("Failed to load split database - color matching disabled");
        colorDatabaseLoaded = false;
    }
    
    // Load saved samples
    loadSamplesFromEEPROM();
    
    // Setup web server
    setupWebServer();
    
    Serial.println("System initialization complete!");
}

void loop() {
    server.handleClient();
    
    // Handle sensor readings if scanning
    if (measurementState.isScanning) {
        // Sensor reading logic would go here
        // For now, just toggle LED to show activity
        measurementState.ledState = !measurementState.ledState;
        if (measurementState.ledState) {
            strip.setPixelColor(0, strip.Color(50, 0, 50));
        } else {
            strip.setPixelColor(0, strip.Color(0, 0, 0));
        }
        strip.show();
    }
    
    delay(10);
}

void setupWiFi() {
    WiFi.begin(ssid, password);
    Serial.print("Connecting to WiFi");
    
    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        Serial.print(".");
    }
    
    Serial.println();
    Serial.print("Connected! IP address: ");
    Serial.println(WiFi.localIP());
}

void setupFileSystem() {
    if (!LittleFS.begin(true)) {
        Serial.println("LittleFS Mount Failed");
        return;
    }
    Serial.println("LittleFS mounted successfully");
}

void setupSensor() {
    Wire.begin();

    if (tcs3430.begin()) {
        Serial.println("TCS3430 sensor initialized");
        // Configure sensor settings
        tcs3430.setIntegrationTime(64);  // ~181ms
        tcs3430.setALSGain(1);          // 4x gain
    } else {
        Serial.println("TCS3430 sensor not found - demo mode");
    }
}

// Check if split database files exist and are accessible
bool loadSplitColorDatabase() {
    Serial.println("Checking split color database files...");

    bool allFilesExist = true;
    size_t totalSize = 0;

    for (int i = 0; i < NUM_DATABASE_FILES; i++) {
        if (!LittleFS.exists(DATABASE_FILES[i])) {
            Serial.printf("ERROR: %s not found\n", DATABASE_FILES[i]);
            allFilesExist = false;
        } else {
            File file = LittleFS.open(DATABASE_FILES[i], "r");
            if (file) {
                size_t fileSize = file.size();
                totalSize += fileSize;
                Serial.printf("Found %s: %d bytes\n", DATABASE_FILES[i], fileSize);
                file.close();
            } else {
                Serial.printf("ERROR: Cannot open %s\n", DATABASE_FILES[i]);
                allFilesExist = false;
            }
        }
    }

    if (!allFilesExist) {
        Serial.println("ERROR: Not all split database files available");
        return false;
    }

    Serial.printf("All %d split database files found (%d bytes total)\n", NUM_DATABASE_FILES, totalSize);
    return true;
}

// Search through a single database file for color matches
DuluxColorMatch searchColorDatabaseFile(const char* filename, uint8_t targetR, uint8_t targetG, uint8_t targetB) {
    DuluxColorMatch bestMatch = {0};
    bestMatch.isValid = false;
    bestMatch.deltaE = 999.0f;
    strcpy(bestMatch.name, "No Match Found");
    strcpy(bestMatch.code, "N/A");
    strcpy(bestMatch.lrv, "N/A");
    strcpy(bestMatch.id, "N/A");
    bestMatch.r = targetR;
    bestMatch.g = targetG;
    bestMatch.b = targetB;

    File file = LittleFS.open(filename, "r");
    if (!file) {
        Serial.printf("Failed to open %s\n", filename);
        return bestMatch;
    }

    size_t fileSize = file.size();
    char* buffer = (char*)ps_malloc(MAX_FILE_BUFFER_SIZE);
    if (!buffer) {
        Serial.printf("Failed to allocate buffer for %s\n", filename);
        file.close();
        return bestMatch;
    }

    size_t bytesRead = file.readBytes(buffer, min(fileSize, MAX_FILE_BUFFER_SIZE - 1));
    buffer[bytesRead] = '\0';
    file.close();

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, buffer);

    if (error) {
        Serial.printf("JSON parsing failed for %s: %s\n", filename, error.c_str());
        free(buffer);
        return bestMatch;
    }

    JsonArray colors = doc.as<JsonArray>();
    int colorsProcessed = 0;

    for (JsonObject color : colors) {
        colorsProcessed++;

        int r = color["r"].as<int>();
        int g = color["g"].as<int>();
        int b = color["b"].as<int>();

        float deltaE = calculateDeltaE(targetR, targetG, targetB, r, g, b);

        if (deltaE < bestMatch.deltaE) {
            bestMatch.deltaE = deltaE;
            bestMatch.isValid = true;
            bestMatch.r = r;
            bestMatch.g = g;
            bestMatch.b = b;

            strncpy(bestMatch.name, color["name"] | "Unknown", sizeof(bestMatch.name) - 1);
            bestMatch.name[sizeof(bestMatch.name) - 1] = '\0';

            strncpy(bestMatch.code, color["code"] | "N/A", sizeof(bestMatch.code) - 1);
            bestMatch.code[sizeof(bestMatch.code) - 1] = '\0';

            strncpy(bestMatch.lrv, color["lrv"] | "N/A", sizeof(bestMatch.lrv) - 1);
            bestMatch.lrv[sizeof(bestMatch.lrv) - 1] = '\0';

            strncpy(bestMatch.id, color["id"] | "N/A", sizeof(bestMatch.id) - 1);
            bestMatch.id[sizeof(bestMatch.id) - 1] = '\0';

            if (deltaE < 1.0f) {
                Serial.printf("Excellent match found: %s (ΔE=%.1f)\n", bestMatch.name, deltaE);
                break;
            }
        }

        if (colorsProcessed % 100 == 0) {
            yield();
        }
    }

    Serial.printf("Processed %d colors from %s, best ΔE: %.1f\n", colorsProcessed, filename, bestMatch.deltaE);
    free(buffer);
    return bestMatch;
}

// Enhanced color matching using split database files
DuluxColorMatch findBestColorMatch(uint8_t targetR, uint8_t targetG, uint8_t targetB) {
    DuluxColorMatch bestMatch = {0};
    bestMatch.isValid = false;
    bestMatch.deltaE = 999.0f;
    strcpy(bestMatch.name, "No Match Found");
    strcpy(bestMatch.code, "N/A");
    strcpy(bestMatch.lrv, "N/A");
    strcpy(bestMatch.id, "N/A");
    bestMatch.r = targetR;
    bestMatch.g = targetG;
    bestMatch.b = targetB;

    unsigned long searchStartTime = millis();
    Serial.printf("SPLIT DATABASE SEARCH: RGB(%d,%d,%d) across %d files\n",
                 targetR, targetG, targetB, NUM_DATABASE_FILES);

    for (int i = 0; i < NUM_DATABASE_FILES; i++) {
        Serial.printf("Searching file %d/%d: %s\n", i+1, NUM_DATABASE_FILES, DATABASE_FILES[i]);

        DuluxColorMatch fileMatch = searchColorDatabaseFile(DATABASE_FILES[i], targetR, targetG, targetB);

        if (fileMatch.deltaE < bestMatch.deltaE) {
            bestMatch = fileMatch;
            Serial.printf("New best match from file %d: %s (ΔE=%.1f)\n",
                         i+1, bestMatch.name, bestMatch.deltaE);
        }

        if (bestMatch.deltaE < 1.0f) {
            Serial.println("Excellent match found, stopping search early");
            break;
        }

        yield();
        delay(10);
    }

    unsigned long searchTime = millis() - searchStartTime;
    Serial.printf("SPLIT DATABASE SEARCH COMPLETE: %lu ms\n", searchTime);
    Serial.printf("Final best match: %s (Code: %s, ΔE=%.1f)\n",
                 bestMatch.name, bestMatch.code, bestMatch.deltaE);

    return bestMatch;
}

// Background color matching with loop prevention - FIXED VERSION
void performBackgroundColorMatching() {
    // Prevent re-entry and enforce minimum time interval
    unsigned long currentTime = millis();
    if (isBackgroundMatchingActive) {
        Serial.println("Background matching already active - skipping");
        return;
    }

    if (currentTime - lastBackgroundMatchingTime < BACKGROUND_MATCHING_INTERVAL) {
        Serial.printf("Background matching too recent (%lu ms ago) - skipping\n",
                     currentTime - lastBackgroundMatchingTime);
        return;
    }

    if (!colorDatabaseLoaded) {
        Serial.println("Color database not loaded - skipping background matching");
        return;
    }

    // Set active flag and update timestamp
    isBackgroundMatchingActive = true;
    lastBackgroundMatchingTime = currentTime;

    Serial.println("=== BACKGROUND COLOR MATCHING START ===");
    Serial.printf("Total samples in storage: %d\n", totalSamples);

    int samplesProcessed = 0;
    int samplesUpdated = 0;
    int samplesSkipped = 0;

    // Only process samples that actually exist (bounds checking)
    for (int i = 0; i < totalSamples && i < MAX_STORED_SAMPLES; i++) {
        int index = (currentSampleIndex - 1 - i + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;

        // Verify sample exists and is valid
        if (!storedSamples[index].isValid) {
            samplesSkipped++;
            continue;
        }

        samplesProcessed++;

        // Check if sample needs color matching (missing or invalid Dulux data)
        bool needsMatching = (strlen(storedSamples[index].duluxName) == 0 ||
                            storedSamples[index].deltaE <= 0.0f ||
                            storedSamples[index].deltaE > 100.0f);

        if (needsMatching) {
            Serial.printf("Processing sample %d: RGB(%d,%d,%d)\n",
                         storedSamples[index].sampleNumber,
                         storedSamples[index].r,
                         storedSamples[index].g,
                         storedSamples[index].b);

            // Perform color matching
            DuluxColorMatch bestMatch = findBestColorMatch(
                storedSamples[index].r,
                storedSamples[index].g,
                storedSamples[index].b
            );

            // Handle poor matches gracefully (ΔE > 50 indicates color outside paint range)
            if (bestMatch.deltaE > 50.0f) {
                Serial.printf("Poor match (ΔE=%.1f) - marking as no match\n", bestMatch.deltaE);
                strcpy(storedSamples[index].duluxName, "No match found");
                strcpy(storedSamples[index].duluxCode, "N/A");
                strcpy(storedSamples[index].duluxLRV, "N/A");
                strcpy(storedSamples[index].duluxID, "N/A");
                storedSamples[index].duluxR = storedSamples[index].r;
                storedSamples[index].duluxG = storedSamples[index].g;
                storedSamples[index].duluxB = storedSamples[index].b;
                storedSamples[index].deltaE = 999.0f; // Mark as processed but no match
            } else {
                // Good match - update sample with match data
                strncpy(storedSamples[index].duluxName, bestMatch.name, sizeof(storedSamples[index].duluxName) - 1);
                storedSamples[index].duluxName[sizeof(storedSamples[index].duluxName) - 1] = '\0';

                strncpy(storedSamples[index].duluxCode, bestMatch.code, sizeof(storedSamples[index].duluxCode) - 1);
                storedSamples[index].duluxCode[sizeof(storedSamples[index].duluxCode) - 1] = '\0';

                strncpy(storedSamples[index].duluxLRV, bestMatch.lrv, sizeof(storedSamples[index].duluxLRV) - 1);
                storedSamples[index].duluxLRV[sizeof(storedSamples[index].duluxLRV) - 1] = '\0';

                strncpy(storedSamples[index].duluxID, bestMatch.id, sizeof(storedSamples[index].duluxID) - 1);
                storedSamples[index].duluxID[sizeof(storedSamples[index].duluxID) - 1] = '\0';

                storedSamples[index].duluxR = bestMatch.r;
                storedSamples[index].duluxG = bestMatch.g;
                storedSamples[index].duluxB = bestMatch.b;
                storedSamples[index].deltaE = bestMatch.deltaE;

                Serial.printf("Good match found: %s (ΔE: %.1f)\n", bestMatch.name, bestMatch.deltaE);
            }

            samplesUpdated++;

            // Yield to prevent watchdog timeout during long processing
            yield();
            delay(10);
        }
    }

    if (samplesUpdated > 0) {
        // Save updated samples to EEPROM
        saveSamplesToEEPROM();
        Serial.printf("Background matching complete: %d/%d samples updated, %d skipped\n",
                     samplesUpdated, samplesProcessed, samplesSkipped);
    } else {
        Serial.printf("Background matching complete: All %d samples already have color data, %d skipped\n",
                     samplesProcessed, samplesSkipped);
    }

    // Clear active flag
    isBackgroundMatchingActive = false;
    Serial.println("=== BACKGROUND COLOR MATCHING END ===");
}
