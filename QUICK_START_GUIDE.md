# ESP32-S3 Color Measurement System - Quick Start Guide

## 🚀 Getting Started

### **1. Power On & Connect**
- Connect ESP32-S3 ProS3 to power via USB-C
- System automatically connects to "Wifi 6" network
- RGB LED shows rainbow cycling when ready
- Open web browser to: **http://***************

### **2. First Time Setup**
1. **Calibrate Sensor** (recommended):
   - Click "Settings" in web interface
   - Run "Dark Calibration" (cover sensor completely)
   - Run "White Balance" (place white paper under sensor)
   - Click "Apply Settings"

2. **Test System**:
   - Click "Start Scan" 
   - RGB LED should show measured color
   - Place colored object near sensor
   - Click "Stop Scan" to save sample

## 🎨 Using the Color Measurement System

### **Basic Color Measurement**
1. **Start Scanning**: Click "Start Scan" button
   - RGB LED immediately shows enhanced measured color
   - Web interface displays live color readings
   - Status shows "Scanning..."

2. **Measure Object**: Place colored object near sensor
   - RGB LED changes to match object color (enhanced for visibility)
   - Live readings update in real-time
   - Move object to see color changes instantly

3. **Save Sample**: Click "Stop Scan" button
   - Green notification: "✓ Sample saved! Total: X/30"
   - Brief color flash effect shows captured color
   - RGB LED returns to rainbow cycling
   - Sample automatically saved to storage

### **Managing Saved Samples**
1. **View Gallery**: Click "📁 View Saved Samples" button
   - Thumbnail grid shows up to 30 saved colors
   - Each thumbnail displays RGB values and Dulux match
   - Empty state message if no samples saved

2. **Sample Details**: Click any thumbnail
   - **Color Information**: RGB, Hex, Confidence percentage
   - **Dulux Match**: Paint name, color swatch, Delta-E accuracy
   - **Raw Sensor Data**: XYZ tristimulus, IR compensation values
   - **Match Quality**: Excellent/Good/Fair/Poor rating

3. **Sample Management**:
   - **Delete Individual**: Click "Delete Sample" in detail view
   - **Clear All**: Click "Clear All Samples" (with confirmation)
   - **Automatic Storage**: Circular buffer (oldest deleted when full)

## 🔧 Advanced Features

### **LED Control**
- **Manual Toggle**: Click "LED: ON/OFF" button
- **Automatic**: LED turns on during scanning and calibration
- **RGB Status**: Shows rainbow when idle, measured color when scanning

### **Sensor Settings** (Settings Panel)
- **Integration Time**: Measurement duration (default: ~100ms)
- **ALS Gain**: Sensor sensitivity (1x, 4x, 16x, 64x)
- **IR Compensation**: Infrared light correction
- **Live Preview**: See settings effects immediately

### **Calibration Tools**
- **White Balance**: Calibrate for current lighting conditions
- **Dark Calibration**: Set sensor baseline (cover completely)
- **Reset Defaults**: Restore factory settings

## 🌈 Understanding Color Readings

### **RGB LED Behavior**
- **Rainbow Cycling**: System idle, ready for measurement
- **Enhanced Colors**: During scanning - colors are boosted for visibility
  - Saturation increased 1.4x for vivid display
  - Brightness normalized for optimal LED range
  - Contrast enhanced for better distinction
  - Gamma corrected for natural appearance

### **Color Accuracy**
- **Measured Values**: Always accurate (enhancement only affects LED display)
- **Confidence Score**: 0-100% measurement reliability
- **Delta-E Rating**: Color difference from Dulux database
  - ≤5.0: Excellent match
  - ≤10.0: Good match  
  - ≤20.0: Fair match
  - >20.0: Poor match

### **Web Interface Sections**
- **Live Readings**: Real-time color measurements (left side)
- **Captured Sample**: Frozen color from last scan (right side)
- **Sample Count**: "Saved Samples: X/30" with green highlighting
- **Status Messages**: Scan progress and save confirmations

## 🛠️ Troubleshooting

### **Common Issues**
1. **Can't Access Web Interface**:
   - Check WiFi connection to "Wifi 6"
   - Verify IP address: *************
   - Wait 30 seconds after power on for full boot

2. **RGB LED Not Working**:
   - System automatically enables LDO2 power control
   - Should show rainbow cycling when idle
   - Contact if LED remains off

3. **Inaccurate Colors**:
   - Run calibration (white balance + dark calibration)
   - Ensure consistent lighting conditions
   - Check sensor is clean and unobstructed

4. **Samples Not Saving**:
   - Green notification should appear after "Stop Scan"
   - Check sample count increases
   - EEPROM automatically stores samples

### **System Reset**
- **Soft Reset**: Click "Reset to Defaults" in settings
- **Hard Reset**: Power cycle the device
- **Clear Samples**: Use "Clear All Samples" button

## 📊 Technical Specifications

### **Hardware**
- **Board**: ESP32-S3 ProS3 (16MB Flash, 8MB PSRAM)
- **Sensor**: TCS3430 color sensor with I2C interface
- **RGB LED**: WS2812 with enhanced color display
- **Network**: WiFi with static IP configuration

### **Performance**
- **Measurement Speed**: 500ms intervals for accuracy
- **Sample Storage**: 30 samples with EEPROM persistence
- **Color Database**: 1500+ Dulux paint colors
- **Web Updates**: 1.5 second refresh rate

### **Memory Usage**
- **Free Heap**: ~287KB (stable operation)
- **PSRAM**: 8MB for color database
- **Sample Storage**: Persistent across power cycles

## 🎯 Best Practices

### **For Accurate Measurements**
1. **Consistent Lighting**: Use built-in illumination LED
2. **Stable Positioning**: Keep sensor close to object surface
3. **Clean Sensor**: Ensure no dust or debris on sensor window
4. **Calibration**: Perform white balance for current conditions

### **For Best Results**
1. **Sample Management**: Regularly review and organize saved samples
2. **Color Comparison**: Use Delta-E values for match quality
3. **Multiple Readings**: Take several measurements for consistency
4. **Documentation**: Note lighting conditions and object materials

## 🏆 System Status

**✅ FULLY OPERATIONAL**
- All hardware components working
- Web interface responsive and feature-complete
- Sample storage and management functional
- Enhanced RGB LED feedback active
- Professional color measurement capabilities

**Access URL**: http://*************
**Support**: Check PROJECT_SUMMARY.md for detailed technical information
