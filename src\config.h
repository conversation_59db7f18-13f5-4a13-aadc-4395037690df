#ifndef CONFIG_H
#define CONFIG_H

// ESP32-S3-Touch-LCD-1.69 Board Configuration
// Based on Waveshare ESP32-S3-Touch-LCD-1.69 specifications

// I2C Configuration (Shared bus for multiple peripherals)
// ESP32-S3-Touch-LCD-1.69 board I2C pins (separate from display SPI)
#define I2C_SDA_PIN        6      // GPIO6 - SDA (shared I2C bus)
#define I2C_SCL_PIN        7      // GPIO7 - SCL (shared I2C bus)
// TCS3430_I2C_ADDRESS is defined within DFRobot_TCS3430 library (usually 0x39)

// Display SPI Configuration (ST7789V2 controller)
// ESP32-S3-Touch-LCD-1.69 built-in 1.69" LCD (240x280, ST7789V2)
// CORRECT PINS from user-provided configuration
#define TFT_SPI_HOST       SPI2_HOST // SPI2_HOST for display
#define TFT_MOSI_PIN       6       // GPIO6 - SDA (MOSI) for ST7789V2
#define TFT_SCLK_PIN       7       // GPIO7 - SCL (Clock) for ST7789V2
#define TFT_CS_PIN         5       // GPIO5 - CSX (Chip Select) for ST7789V2
#define TFT_DC_PIN         4       // GPIO4 - D/CX (Data/Command) for ST7789V2
#define TFT_RST_PIN        8       // GPIO8 - RESX (Reset) for ST7789V2
#define TFT_BL_PIN         15      // GPIO15 - Backlight
//#define TFT_BL_PIN         16      // GPIO16 - Backlight control (alternative pin, GPIO4 may conflict)

// Display resolution (also in platformio.ini build_flags for LVGL if needed)
#ifndef TFT_WIDTH
#define TFT_WIDTH          240
#endif
#ifndef TFT_HEIGHT
#define TFT_HEIGHT         280
#endif

// Touch Controller (CST816T - built-in on ESP32-S3-Touch-LCD-1.69)
// Based on official Waveshare documentation and CST816T register declaration
#define TOUCH_I2C_ADDRESS  0x15  // CST816T I2C address (7-bit: 0x15, write: 0x2A, read: 0x2B)
#define TOUCH_INT_PIN      9     // GPIO9 - Touch interrupt pin
#define TOUCH_RST_PIN      13    // GPIO13 - Touch reset pin
// Touch uses the same I2C bus as other peripherals (GPIO6/7)

// Onboard Peripherals (ESP32-S3-Touch-LCD-1.69 specific)
#define BUZZER_PIN         42    // GPIO42 - Onboard buzzer
#define PWM_BUTTON_PIN     0     // GPIO0 - PWM button (supports single/double/long press)
#define BOOT_BUTTON_PIN    0     // GPIO0 - BOOT button (same as PWM button)
#define RTC_INT_PIN        39    // GPIO39 - RTC interrupt pin
#define SYS_EN_PIN         41    // GPIO41 - System power enable control
#define SYS_OUT_PIN        40    // GPIO40 - System power output control

// External sensor connections (using available GPIO pins)
#define SENSOR_INTERRUPT_PIN    17   // GPIO17 - Interrupt pin for TCS3430 (INT line, GPIO2 used by TFT_DC)
#define RGB_LED_PIN        18    // GPIO18 - For external RGB LED or status indicator

// Onboard I2C Peripherals (sharing the same I2C bus)
#define RTC_I2C_ADDRESS    0x51  // PCF85063 RTC chip I2C address
#define IMU_I2C_ADDRESS    0x6B  // QMI8658C 6-axis IMU I2C address (typical)

// Application Settings
#define SENSOR_READ_INTERVAL_MS 1000 // Sensor reading interval in milliseconds

// Color Matching Settings
#define DELTA_E_TOLERANCE  3.0f // Tolerance for declaring a "MATCH" (Delta E CIE76)
                                // Lower is stricter. 2-3 is often a good starting point.

#endif // CONFIG_H