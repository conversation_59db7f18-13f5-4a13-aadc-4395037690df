#ifndef CALIBRATION_H
#define CALIBRATION_H

#include <Arduino.h>

typedef struct {
    float k_ir_compensation_factor; // Factor for `apply_ir_compensation`
    float display_srgb_norm_factor; // Normalization factor for `xyz_to_display_srgb`
                                    // Typically, the sensor's max Y reading for a white reference.
    // Example for future full XYZ normalization/Chromatic Adaptation:
    // float white_ref_sensor_X_raw;   // Raw X sensor reading for your white reference
    // float white_ref_sensor_Y_raw;   // Raw Y sensor reading for your white reference
    // float white_ref_sensor_Z_raw;   // Raw Z sensor reading for your white reference
    bool data_is_valid;             // True if calibration data is loaded/set, false for defaults
} app_calibration_data_t;

extern app_calibration_data_t current_app_calibration; // Global calibration data instance

void calibration_init(); // Load from NVM or set default values
void calibration_run_procedure(); // Placeholder for an interactive calibration routine
bool calibration_save(); // Save current calibration data to NVM
bool calibration_load(); // Load calibration data from NVM

#endif // CALIBRATION_H