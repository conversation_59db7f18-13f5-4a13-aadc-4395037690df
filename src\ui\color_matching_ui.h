#ifndef COLOR_MATCHING_UI_H
#define COLOR_MATCHING_UI_H

#include "lvgl.h"                // LVGL library
#include "../color_conversion.h" // For rgb_color_s, xyz_color_s structures

// Initializes all LVGL objects for the color matching UI.
void ui_init(void);

// Updates the UI elements with new color data.
// - display_rgb: The sRGB color to show on the swatch.
// - raw_xyz: The (possibly IR-compensated) XYZ sensor values.
// - ir1, ir2: Raw IR sensor readings.
// - matched_color_name: Name of the closest color from the database.
// - delta_e: Calculated Delta E value for the match.
void update_color_display(
    const rgb_color_s &display_rgb,
    const xyz_color_s &raw_xyz,
    uint16_t ir1,
    uint16_t ir2,
    const char* matched_color_name,
    float delta_e
);

// Displays a message (error or informational) on the UI.
// If is_error is true, message might be styled as an error (e.g., red background).
void show_ui_message(const char* msg, bool is_error);

// Clears any currently displayed message from the UI.
void clear_ui_message(void);

#endif // COLOR_MATCHING_UI_H