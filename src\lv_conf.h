#ifndef LV_CONF_H
#define LV_CONF_H

#include <stdint.h> // Required for LVGL types

/*=========================
   MEMORY SETTINGS
 *=========================*/
// Amount of memory available for LVGL. Reduced since no PSRAM detected.
#define LV_MEM_SIZE    (48U * 1024U)  /*[bytes]*/

/*=========================
   DISPLAY SETTINGS
 *=========================*/
// Color depth: 16 bits (RGB565)
#define LV_COLOR_DEPTH  16
// Swap the 2 bytes of RGB565 color. Useful if colors are inverted.
#define LV_COLOR_16_SWAP 0

/*=========================
   FONT SETTINGS
 *=========================*/
// Enable built-in Montserrat fonts
#define LV_FONT_MONTSERRAT_14    1
#define LV_FONT_MONTSERRAT_20    1
// Set default font
#define LV_FONT_DEFAULT          &lv_font_montserrat_14

/*=========================
   THEME SETTINGS
 *=========================*/
#define LV_USE_THEME_DEFAULT 1
#if LV_USE_THEME_DEFAULT
    #define LV_THEME_DEFAULT_DARK 0 // Use light theme
    #define LV_THEME_DEFAULT_FONT &lv_font_default
#endif

/*=========================
   TEXT SETTINGS
 *=========================*/
#define LV_USE_ARABIC_PERSIAN_CHARS 0
#define LV_USE_BIDI 0

/*=========================
   INPUT DEVICE SETTINGS
 *=========================*/
// Enable touch input support for CST816T
#define LV_USE_INDEV_POINTER 1
#define LV_INDEV_DEF_READ_PERIOD 30    // Input device read period in milliseconds
#define LV_INDEV_DEF_DRAG_LIMIT  10    // Drag threshold in pixels
#define LV_INDEV_DEF_DRAG_THROW  10    // Drag throw slow-down in [%]
#define LV_INDEV_DEF_LONG_PRESS_TIME 400 // Long press time in milliseconds

/*=========================
   WIDGETS
 *=========================*/
// Enable necessary widgets
#define LV_USE_LABEL    1
#define LV_USE_BTN      1
#define LV_USE_OBJ      1 // Base object
#define LV_USE_FLEX     1 // Enable flex layout
#define LV_USE_IMG      1 // Required by LVGL 8.x for canvas and animimg widgets
#define LV_USE_CANVAS   0 // Canvas widget (not needed for this project)
#define LV_USE_ANIMIMG  0 // Animated image widget (not needed for this project)

/*=========================
   MISC SETTINGS
 *=========================*/
// Enable custom tick source for ESP32
#define LV_TICK_CUSTOM          1
#if LV_TICK_CUSTOM
    #define LV_TICK_CUSTOM_INCLUDE  "Arduino.h"       // Arduino's millis()
    #define LV_TICK_CUSTOM_SYS_TIME_EXPR (millis())
#endif

// Log settings for LVGL
#define LV_USE_LOG              1
#if LV_USE_LOG
    #define LV_LOG_LEVEL    LV_LOG_LEVEL_INFO // Or WARN, ERROR, TRACE, NONE
    #define LV_LOG_PRINTF   1   // Enable printf-style logging
#endif

// Enable UTF-8 support for text
#define LV_TXT_ENC LV_TXT_ENC_UTF8

// Ensure this is included at the end for internal configurations
#include "lv_conf_internal.h" // Or lvgl/src/lv_conf_internal.h if not found

#endif /*LV_CONF_H*/