# ESP32-S3-Touch-LCD-1.69 Display Troubleshooting

## 🔍 **Current Issue: Black Screen with Backlight On**

### **Symptoms**
- ✅ Boot beep heard (firmware is running)
- ✅ Backlight turns on (power and backlight control working)
- ❌ Screen remains black (display initialization issue)

## **Diagnostic Steps**

### **Step 1: Check Serial Output**
Connect to serial monitor at **115200 baud** and look for these messages:

**Expected Boot Sequence:**
```
[I] [System]: Booting up Color Matcher application...
[I] [I2C]: I2C interface initialized.
[I] [Buzzer]: Buzzer initialized on GPIO42.
[I] [Power]: Power control initialized. SYS_EN=GPIO41, SYS_OUT=GPIO40
[I] [Display]: Initializing display with pins: MOSI=6, SCK=7, CS=5, DC=4, RST=8, BL=15
[I] [Display]: Starting display initialization...
[I] [Display]: Display begin() successful!
[I] [Display]: Drawing test patterns...
[I] [Display]: Test patterns complete. GFX display driver initialized.
[I] [Display]: Backlight turned ON.
```

**If you see error messages, note them down!**

### **Step 2: Try Alternative Pin Configurations**

The ESP32-S3-Touch-LCD-1.69 might use different pins. Try these alternatives in `src/config.h`:

#### **Option A: Common ESP32-S3 Display Pins**
```cpp
#define TFT_MOSI_PIN       11      // GPIO11 - MOSI
#define TFT_SCLK_PIN       12      // GPIO12 - SCK  
#define TFT_CS_PIN         10      // GPIO10 - CS
#define TFT_DC_PIN         14      // GPIO14 - DC
#define TFT_RST_PIN        21      // GPIO21 - RST
#define TFT_BL_PIN         48      // GPIO48 - Backlight
```

#### **Option B: Alternative Configuration**
```cpp
#define TFT_MOSI_PIN       35      // GPIO35 - MOSI
#define TFT_SCLK_PIN       36      // GPIO36 - SCK
#define TFT_CS_PIN         34      // GPIO34 - CS  
#define TFT_DC_PIN         37      // GPIO37 - DC
#define TFT_RST_PIN        38      // GPIO38 - RST
#define TFT_BL_PIN         45      // GPIO45 - Backlight
```

#### **Option C: Check Board Silkscreen**
Look at your physical board for pin labels near the display connector.

### **Step 3: Try Different ST7789 Parameters**

In `src/main.cpp`, try these alternatives:

#### **Alternative 1: No Offsets**
```cpp
gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0, true, TFT_WIDTH, TFT_HEIGHT);
```

#### **Alternative 2: Different Rotation**
```cpp
gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 1, true, TFT_WIDTH, TFT_HEIGHT);
```

#### **Alternative 3: Non-IPS Panel**
```cpp
gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0, false, TFT_WIDTH, TFT_HEIGHT);
```

#### **Alternative 4: Different Offsets**
```cpp
gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0, true, TFT_WIDTH, TFT_HEIGHT, 0, 0, 0, 0);
```

### **Step 4: SPI Frequency Issues**

Try slower SPI speeds in `src/main.cpp`:

```cpp
// Very slow for testing
bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1, TFT_SPI_HOST, 10000000);

// Or even slower
bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1, TFT_SPI_HOST, 1000000);
```

### **Step 5: Hardware Verification**

#### **Check Physical Connections**
1. **Power**: Ensure 3.3V and GND are connected
2. **SPI Lines**: Verify MOSI, SCK, CS, DC, RST connections
3. **Backlight**: Confirm backlight control pin

#### **Multimeter Testing**
- **3.3V on VCC pin**: Should read ~3.3V
- **Backlight voltage**: Should change when toggling GPIO15
- **SPI activity**: Should see signals on MOSI/SCK during initialization

### **Step 6: Quick Test Firmware**

Create a minimal test to isolate the display issue:

```cpp
#include <Arduino.h>
#include <Arduino_GFX_Library.h>

// Test pins - adjust as needed
#define TFT_MOSI_PIN       6
#define TFT_SCLK_PIN       7  
#define TFT_CS_PIN         5
#define TFT_DC_PIN         4
#define TFT_RST_PIN        8
#define TFT_BL_PIN         15

Arduino_DataBus *bus;
Arduino_GFX *gfx;

void setup() {
    Serial.begin(115200);
    Serial.println("Display Test Starting...");
    
    // Backlight on
    pinMode(TFT_BL_PIN, OUTPUT);
    digitalWrite(TFT_BL_PIN, HIGH);
    
    // Initialize display
    bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1, SPI2_HOST, 10000000);
    gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0, true, 240, 280);
    
    if (gfx->begin()) {
        Serial.println("Display initialized!");
        gfx->fillScreen(RED);
        delay(1000);
        gfx->fillScreen(GREEN);
        delay(1000);
        gfx->fillScreen(BLUE);
    } else {
        Serial.println("Display initialization failed!");
    }
}

void loop() {
    delay(1000);
}
```

## **Common Solutions**

### **Solution 1: Wrong Pins**
- Check board documentation or silkscreen
- Try common ESP32-S3 display pin combinations
- Use multimeter to trace connections

### **Solution 2: Display Parameters**
- Try different rotation values (0, 1, 2, 3)
- Toggle IPS panel setting (true/false)
- Adjust offset values for proper alignment

### **Solution 3: SPI Issues**
- Reduce SPI frequency (try 10MHz, 1MHz)
- Check SPI host (try SPI3_HOST instead of SPI2_HOST)
- Verify SPI pin assignments

### **Solution 4: Power Issues**
- Ensure stable 3.3V supply
- Check if display needs separate power enable
- Verify ground connections

### **Solution 5: Reset Timing**
- Add delays around reset sequence
- Try different reset pin assignments
- Manual reset pulse before initialization

## **Next Steps**

1. **Upload the diagnostic firmware** with test patterns
2. **Check serial output** for error messages
3. **Try alternative pin configurations** one by one
4. **Test with minimal code** to isolate the issue
5. **Check hardware connections** with multimeter

## **Expected Results**

After fixing the pin configuration, you should see:
- **Color test patterns** (red, green, blue, white, black)
- **LVGL interface** with color swatch and data
- **Touch responsiveness** 
- **Serial log messages** confirming successful initialization

## **Get Help**

If none of these solutions work:
1. **Share serial output** - Copy the complete boot log
2. **Verify board model** - Confirm exact ESP32-S3-Touch-LCD-1.69 variant
3. **Check documentation** - Look for official Waveshare pin diagrams
4. **Hardware inspection** - Photos of board connections can help diagnose
