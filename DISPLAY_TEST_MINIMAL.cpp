/*
 * Minimal ESP32-S3-Touch-LCD-1.69 Display Test
 * This is a simplified test to verify display functionality
 * without LVGL or touch controller interference
 */

#include <Arduino.h>
#include <SPI.h>
#include <Arduino_GFX_Library.h>

// ESP32-S3-Touch-LCD-1.69 Display Pins (ST7789V2)
#define TFT_MOSI_PIN       6       // GPIO6 - SDA (MOSI)
#define TFT_SCLK_PIN       7       // GPIO7 - SCL (Clock)
#define TFT_CS_PIN         5       // GPIO5 - CSX (Chip Select)
#define TFT_DC_PIN         4       // GPIO4 - D/CX (Data/Command)
#define TFT_RST_PIN        8       // GPIO8 - RESX (Reset)
#define TFT_BL_PIN         15      // GPIO15 - Backlight

// Display resolution
#define TFT_WIDTH          240
#define TFT_HEIGHT         280

// Buzzer for audio feedback
#define BUZZER_PIN         42      // GPIO42

Arduino_DataBus *bus;
Arduino_GFX *gfx;

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=== ESP32-S3-Touch-LCD-1.69 Display Test ===");
    Serial.println("Board: Waveshare ESP32-S3-Touch-LCD-1.69");
    Serial.println("Display: 1.69\" ST7789V2 (240x280)");
    
    // Initialize buzzer
    pinMode(BUZZER_PIN, OUTPUT);
    digitalWrite(BUZZER_PIN, LOW);
    
    // Boot beep
    tone(BUZZER_PIN, 1000, 200);
    delay(300);
    
    // Initialize backlight
    pinMode(TFT_BL_PIN, OUTPUT);
    digitalWrite(TFT_BL_PIN, HIGH); // Turn on backlight
    Serial.println("Backlight: ON");
    
    // Initialize SPI bus for display
    Serial.println("Initializing SPI bus...");
    bus = new Arduino_ESP32SPI(TFT_DC_PIN, TFT_CS_PIN, TFT_SCLK_PIN, TFT_MOSI_PIN, -1, SPI2_HOST, 40000000);
    
    // Initialize ST7789V2 display
    Serial.println("Initializing ST7789V2 display...");
    // Try different configurations
    gfx = new Arduino_ST7789(bus, TFT_RST_PIN, 0 /* rotation */, true /* IPS */, TFT_WIDTH, TFT_HEIGHT, 0, 20, 0, 20);
    
    if (!gfx->begin()) {
        Serial.println("ERROR: Display initialization failed!");
        // Error beep pattern
        for (int i = 0; i < 3; i++) {
            tone(BUZZER_PIN, 500, 200);
            delay(300);
        }
        while (1) {
            delay(1000);
        }
    }
    
    Serial.println("SUCCESS: Display initialized!");
    
    // Success beep
    tone(BUZZER_PIN, 1500, 300);
    delay(400);
    
    // Test pattern sequence
    Serial.println("Drawing test patterns...");
    
    // Fill screen with colors
    Serial.println("Red screen...");
    gfx->fillScreen(RED);
    delay(1000);
    
    Serial.println("Green screen...");
    gfx->fillScreen(GREEN);
    delay(1000);
    
    Serial.println("Blue screen...");
    gfx->fillScreen(BLUE);
    delay(1000);
    
    Serial.println("White screen...");
    gfx->fillScreen(WHITE);
    delay(1000);
    
    Serial.println("Black screen...");
    gfx->fillScreen(BLACK);
    delay(500);
    
    // Draw text
    Serial.println("Drawing text...");
    gfx->setTextColor(WHITE);
    gfx->setTextSize(2);
    gfx->setCursor(10, 50);
    gfx->println("ESP32-S3");
    gfx->setCursor(10, 80);
    gfx->println("Display");
    gfx->setCursor(10, 110);
    gfx->println("Working!");
    
    gfx->setTextSize(1);
    gfx->setCursor(10, 150);
    gfx->println("ST7789V2 Controller");
    gfx->setCursor(10, 170);
    gfx->println("240x280 Resolution");
    gfx->setCursor(10, 190);
    gfx->println("Waveshare Board");
    
    // Draw some shapes
    Serial.println("Drawing shapes...");
    gfx->drawRect(10, 210, 100, 50, YELLOW);
    gfx->fillRect(15, 215, 90, 40, BLUE);
    
    gfx->drawCircle(180, 235, 30, RED);
    gfx->fillCircle(180, 235, 25, GREEN);
    
    Serial.println("Display test complete!");
    
    // Final success beep pattern
    tone(BUZZER_PIN, 1000, 100);
    delay(150);
    tone(BUZZER_PIN, 1200, 100);
    delay(150);
    tone(BUZZER_PIN, 1500, 200);
    
    Serial.println("=== Test Results ===");
    Serial.println("If you can see colors and text on the display,");
    Serial.println("the ST7789V2 display is working correctly!");
    Serial.println("Pin configuration is correct for your board.");
}

void loop() {
    // Cycle through different background colors
    static unsigned long lastUpdate = 0;
    static int colorIndex = 0;
    uint16_t colors[] = {BLACK, RED, GREEN, BLUE, YELLOW, CYAN, MAGENTA, WHITE};
    
    if (millis() - lastUpdate > 3000) {
        lastUpdate = millis();
        
        gfx->fillScreen(colors[colorIndex]);
        
        // Keep text visible
        gfx->setTextColor(colors[colorIndex] == WHITE ? BLACK : WHITE);
        gfx->setTextSize(2);
        gfx->setCursor(10, 50);
        gfx->println("ESP32-S3");
        gfx->setCursor(10, 80);
        gfx->println("Display");
        gfx->setCursor(10, 110);
        gfx->println("Working!");
        
        gfx->setTextSize(1);
        gfx->setCursor(10, 150);
        gfx->printf("Color: %d/8", colorIndex + 1);
        
        colorIndex = (colorIndex + 1) % 8;
        
        Serial.printf("Color cycle: %d/8\n", colorIndex);
    }
    
    delay(100);
}
