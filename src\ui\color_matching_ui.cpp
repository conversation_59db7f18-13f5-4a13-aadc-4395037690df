#include "color_matching_ui.h"
#include "../config.h"    // For UI layout constants if any
#include "../utilities.h" // For logging
#include "../calibration.h" // For calling calibration_run_procedure

// Static LVGL object handles for UI elements
static lv_obj_t* header_label_obj;
static lv_obj_t* color_swatch_obj;
static lv_obj_t* xyz_label_obj;
static lv_obj_t* rgb_label_obj;
static lv_obj_t* ir_label_obj;
static lv_obj_t* matched_color_label_obj;
static lv_obj_t* delta_e_label_obj;
static lv_obj_t* calibrate_button_obj;
static lv_obj_t* settings_button_obj; // Placeholder
static lv_obj_t* message_label_obj = NULL; // For displaying errors or info messages

// Event handler for the Calibrate button
static void calibrate_btn_event_handler(lv_event_t * e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_CLICKED) {
        log_info("UI", "Calibrate button clicked.");
        // Call the actual calibration procedure
        calibration_run_procedure();
        // show_ui_message("Calibration procedure started (placeholder).", false);
    }
}

// Event handler for the Settings button (placeholder)
static void settings_btn_event_handler(lv_event_t * e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_CLICKED) {
        log_info("UI", "Settings button clicked.");
        show_ui_message("Settings screen not implemented.", false);
    }
}

void ui_init(void) {
    lv_obj_t * screen = lv_scr_act();
    lv_obj_set_style_bg_color(screen, lv_color_hex(0xF4F4F8), LV_PART_MAIN); // Light, slightly bluish-grey background

    // --- Header Panel ---
    lv_obj_t *header_panel = lv_obj_create(screen);
    lv_obj_set_size(header_panel, lv_pct(100), 45); // Percentage width, fixed height
    lv_obj_align(header_panel, LV_ALIGN_TOP_MID, 0, 0);
    lv_obj_set_style_bg_color(header_panel, lv_color_hex(0x3F51B5), LV_PART_MAIN); // Material Indigo
    lv_obj_set_style_radius(header_panel, 0, LV_PART_MAIN); // No rounded corners for header bar
    lv_obj_set_style_border_width(header_panel, 0, LV_PART_MAIN);

    header_label_obj = lv_label_create(header_panel);
    lv_label_set_text(header_label_obj, "ESP32 ColorSense");
    lv_obj_set_style_text_color(header_label_obj, lv_color_white(), LV_PART_MAIN);
    lv_obj_set_style_text_font(header_label_obj, &lv_font_montserrat_20, LV_PART_MAIN);
    lv_obj_center(header_label_obj);

    // --- Main Content Area (using Flexbox for layout) ---
    lv_obj_t* content_area = lv_obj_create(screen);
    lv_obj_set_size(content_area, lv_pct(95), lv_pct(75)); // Takes up most of the screen below header
    lv_obj_align(content_area, LV_ALIGN_CENTER, 0, 20);    // Positioned in center, slightly offset for header
    lv_obj_set_flex_flow(content_area, LV_FLEX_FLOW_COLUMN); // Arrange children vertically
    lv_obj_set_flex_align(content_area, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER); // Main, Cross, Track
    lv_obj_set_style_pad_all(content_area, 5, LV_PART_MAIN); // Padding inside content area
    lv_obj_set_style_bg_opa(content_area, LV_OPA_TRANSP, LV_PART_MAIN); // Transparent background
    lv_obj_set_style_border_width(content_area, 0, LV_PART_MAIN);


    // --- Color Swatch ---
    color_swatch_obj = lv_obj_create(content_area); // Add to content area
    lv_obj_set_size(color_swatch_obj, 100, 100);
    lv_obj_set_style_bg_color(color_swatch_obj, lv_color_black(), LV_PART_MAIN); // Default color
    lv_obj_set_style_radius(color_swatch_obj, 8, LV_PART_MAIN);
    lv_obj_set_style_border_color(color_swatch_obj, lv_color_hex(0x757575), LV_PART_MAIN); // Grey border
    lv_obj_set_style_border_width(color_swatch_obj, 1, LV_PART_MAIN);
    lv_obj_set_style_shadow_width(color_swatch_obj, 12, LV_PART_MAIN);
    lv_obj_set_style_shadow_opa(color_swatch_obj, LV_OPA_20, LV_PART_MAIN);
    lv_obj_set_style_shadow_ofs_y(color_swatch_obj, 4, LV_PART_MAIN);

    // --- Data Labels Container (also using Flexbox) ---
    lv_obj_t* data_labels_container = lv_obj_create(content_area); // Add to content area
    lv_obj_set_width(data_labels_container, lv_pct(100));
    lv_obj_set_height(data_labels_container, LV_SIZE_CONTENT); // Auto height based on labels
    lv_obj_set_flex_flow(data_labels_container, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(data_labels_container, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_row(data_labels_container, 4, LV_PART_MAIN); // Space between labels
    lv_obj_set_style_bg_opa(data_labels_container, LV_OPA_TRANSP, LV_PART_MAIN);
    lv_obj_set_style_border_width(data_labels_container, 0, LV_PART_MAIN);

    xyz_label_obj = lv_label_create(data_labels_container);
    lv_label_set_text(xyz_label_obj, "X: ---  Y: ---  Z: ---");
    lv_obj_set_style_text_font(xyz_label_obj, &lv_font_montserrat_14, LV_PART_MAIN);

    rgb_label_obj = lv_label_create(data_labels_container);
    lv_label_set_text(rgb_label_obj, "R: ---  G: ---  B: ---");
    lv_obj_set_style_text_font(rgb_label_obj, &lv_font_montserrat_14, LV_PART_MAIN);
    
    ir_label_obj = lv_label_create(data_labels_container);
    lv_label_set_text(ir_label_obj, "IR1: ---  IR2: ---");
    lv_obj_set_style_text_font(ir_label_obj, &lv_font_montserrat_14, LV_PART_MAIN);

    matched_color_label_obj = lv_label_create(data_labels_container);
    lv_label_set_text(matched_color_label_obj, "Match: Initializing...");
    lv_obj_set_style_text_font(matched_color_label_obj, &lv_font_montserrat_14, LV_PART_MAIN);
    lv_obj_set_style_pad_top(matched_color_label_obj, 6, LV_PART_MAIN); // Extra space above match name

    delta_e_label_obj = lv_label_create(data_labels_container);
    lv_label_set_text(delta_e_label_obj, "ΔE: ---");
    lv_obj_set_style_text_font(delta_e_label_obj, &lv_font_montserrat_14, LV_PART_MAIN);


    // --- Buttons Container (at the bottom of the screen) ---
    lv_obj_t* btn_container = lv_obj_create(screen);
    lv_obj_set_width(btn_container, lv_pct(100));
    lv_obj_set_height(btn_container, LV_SIZE_CONTENT); // Auto height
    lv_obj_align(btn_container, LV_ALIGN_BOTTOM_MID, 0, -10); // Position at bottom with padding
    lv_obj_set_flex_flow(btn_container, LV_FLEX_FLOW_ROW); // Arrange buttons horizontally
    lv_obj_set_flex_align(btn_container, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_bg_opa(btn_container, LV_OPA_TRANSP, LV_PART_MAIN);
    lv_obj_set_style_border_width(btn_container, 0, LV_PART_MAIN);

    calibrate_button_obj = lv_btn_create(btn_container);
    lv_obj_add_event_cb(calibrate_button_obj, calibrate_btn_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_t* cal_label = lv_label_create(calibrate_button_obj);
    lv_label_set_text(cal_label, "Calibrate");
    lv_obj_center(cal_label);

    settings_button_obj = lv_btn_create(btn_container);
    lv_obj_add_event_cb(settings_button_obj, settings_btn_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_t* set_label = lv_label_create(settings_button_obj);
    lv_label_set_text(set_label, "Settings");
    lv_obj_center(set_label);
}

void update_color_display(
    const rgb_color_s &display_rgb,
    const xyz_color_s &raw_xyz,
    uint16_t ir1,
    uint16_t ir2,
    const char* matched_color_name,
    float delta_e) {

    if (!lv_is_initialized()) return; // Guard if LVGL not ready

    // Update swatch color
    if (color_swatch_obj) {
        lv_obj_set_style_bg_color(color_swatch_obj, lv_color_make(display_rgb.r, display_rgb.g, display_rgb.b), LV_PART_MAIN);
    }

    char buf[120]; // Buffer for formatting text
    // Update XYZ numeric labels
    if (xyz_label_obj) {
        sprintf(buf, "X: %5.0f  Y: %5.0f  Z: %5.0f", raw_xyz.x, raw_xyz.y, raw_xyz.z);
        lv_label_set_text(xyz_label_obj, buf);
    }
    // Update RGB numeric labels
    if (rgb_label_obj) {
        sprintf(buf, "R: %3u  G: %3u  B: %3u", display_rgb.r, display_rgb.g, display_rgb.b);
        lv_label_set_text(rgb_label_obj, buf);
    }
    // Update IR label
    if (ir_label_obj) {
        sprintf(buf, "IR1: %5u  IR2: %5u", ir1, ir2);
        lv_label_set_text(ir_label_obj, buf);
    }
    // Update Matched Color Name label
    if (matched_color_label_obj) {
        sprintf(buf, "Match: %s", (matched_color_name && strlen(matched_color_name) > 0) ? matched_color_name : "---");
        lv_label_set_text(matched_color_label_obj, buf);
    }
    // Update Delta E label
    if (delta_e_label_obj) {
        if (matched_color_name && strcmp(matched_color_name, "Unknown") != 0 && strcmp(matched_color_name, "---") != 0 && delta_e >= 0.0f) {
             sprintf(buf, "ΔE: %.2f", delta_e);
        } else {
             sprintf(buf, "ΔE: ---");
        }
        lv_label_set_text(delta_e_label_obj, buf);
    }
}

void show_ui_message(const char *msg, bool is_error) {
    if (!lv_is_initialized()) { // LVGL not ready
        log_message(is_error ? LOG_LEVEL_ERROR : LOG_LEVEL_INFO, "UI_Msg", msg); // Log to serial instead
        return;
    }

    if (message_label_obj == NULL) { // Create message label if it doesn't exist
        message_label_obj = lv_label_create(lv_scr_act()); // Create on active screen
        lv_obj_set_style_text_color(message_label_obj, lv_color_white(), LV_PART_MAIN);
        lv_obj_set_style_pad_all(message_label_obj, 8, LV_PART_MAIN); // Padding around text
        lv_obj_set_style_radius(message_label_obj, 5, LV_PART_MAIN);  // Rounded corners
        lv_obj_align(message_label_obj, LV_ALIGN_BOTTOM_MID, 0, -55); // Position above buttons
        lv_obj_set_width(message_label_obj, lv_pct(85));             // Width as percentage of screen
        lv_label_set_long_mode(message_label_obj, LV_LABEL_LONG_WRAP); // Wrap long text
        lv_obj_add_flag(message_label_obj, LV_OBJ_FLAG_CLICKABLE);     // Make it clickable to dismiss
        lv_obj_add_event_cb(message_label_obj, [](lv_event_t * e) {   // Click event handler
            if (lv_event_get_code(e) == LV_EVENT_CLICKED) {
                clear_ui_message(); // Dismiss on click
            }
        }, LV_EVENT_ALL, NULL);
    }

    // Set background color based on message type
    if (is_error) {
        lv_obj_set_style_bg_color(message_label_obj, lv_color_hex(0xE53935), LV_PART_MAIN); // Material Red for errors
    } else {
        lv_obj_set_style_bg_color(message_label_obj, lv_color_hex(0x43A047), LV_PART_MAIN); // Material Green for info
    }
    lv_label_set_text(message_label_obj, msg); // Set the message text
    lv_obj_clear_flag(message_label_obj, LV_OBJ_FLAG_HIDDEN); // Make sure it's visible
    lv_obj_move_foreground(message_label_obj); // Bring to front to overlay other elements if necessary
}

void clear_ui_message(void) {
    if (message_label_obj != NULL && lv_obj_is_valid(message_label_obj)) { // Check if label exists and is valid
         lv_obj_add_flag(message_label_obj, LV_OBJ_FLAG_HIDDEN); // Hide it
    }
}