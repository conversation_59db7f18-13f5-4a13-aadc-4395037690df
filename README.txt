# ESP32 Color Matching Application

Firmware for an ESP32-based color matching device using a TCS3430 sensor and an ST7789 touchscreen display with LVGL.

## Project Structure

- `platformio.ini`: PlatformIO project configuration.
- `src/`: Main source code.
  - `main.cpp`: Main application logic, setup, and loop.
  - `config.h`: Hardware pin definitions and application constants.
  - `lv_conf.h`: LVGL library configuration.
  - `calibration.h/.cpp`: Handles color calibration data and procedures.
  - `color_conversion.h/.cpp`: Functions for color space conversions (XYZ, RGB, Lab).
  - `color_database.h/.cpp`: Manages a database of reference colors for matching.
  - `utilities.h/.cpp`: Helper functions, logging, and RGB LED control.
  - `ui/`: LVGL-based user interface files.
    - `color_matching_ui.h/.cpp`: UI initialization and update functions.

## Libraries Used

- `Wire`: For I2C communication.
- `lvgl/lvgl`: For the graphical user interface.
- `moononournation/GFX Library for Arduino`: For ST7789 display driving.
- `DFRobot/DFRobot_TCS3430`: Driver for the TCS3430 color sensor.
- (Optional) CST816 touch controller library if generic I2C is insufficient.

## Setup

1.  **Hardware Connections:** Ensure all components (ESP32, TCS3430, ST7789 display, touch controller) are connected according to the pins defined in `src/config.h`.
2.  **PlatformIO:** Open this project in VS Code with the PlatformIO extension. Libraries should be automatically downloaded.
3.  **Board Selection:** Verify that the `board` type in `platformio.ini` matches your specific ESP32-S3 board.
4.  **Build & Upload:** Build and upload the firmware to your ESP32 device.

## Calibration

- The current calibration module (`calibration.cpp`) provides stubs for loading/saving data (e.g., from NVS) and an interactive procedure. These need to be fully implemented.
- Key calibration parameters:
  - `k_ir_compensation_factor` in `calibration_data_t`.
  - `display_srgb_norm_factor` in `calibration_data_t`.
  - Accurate color matching requires proper calibration against a known white reference under controlled lighting (ideally D65).

## Notes

- The touch input (`touchpad_read_cb` in `main.cpp`) uses a generic I2C read for the CST816. For more robust touch handling, consider integrating a dedicated CST816 library.
- The `setCH0IntThreshold` from the DFRobot TCS3430 library is used for sensor interrupts. This typically refers to the Clear/X channel. If interrupt based on Z-channel was intended, the library or approach might need adjustment.