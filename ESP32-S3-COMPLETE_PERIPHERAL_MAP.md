# ESP32-S3-Touch-LCD-1.69 Complete Peripheral Mapping

## 🎯 **FINAL CONFIGURATION - ALL PERIPHERALS MAPPED**

### **Complete Pin Assignment Table**

| **Peripheral** | **Function** | **GPIO** | **Description** |
|----------------|--------------|----------|-----------------|
| **Display (ST7789V2)** | | | **SPI Interface** |
| | MOSI (SDA) | GPIO6 | Display data line |
| | SCK (SCL) | GPIO7 | Display clock |
| | CS (CSX) | GPIO5 | Chip select |
| | DC (D/CX) | GPIO4 | Data/Command |
| | RST (RESX) | GPIO8 | Reset |
| | Backlight | GPIO15 | PWM backlight control |
| **Touch (CST816D)** | | | **I2C Interface** |
| | I2C Address | 0x15 | 7-bit address |
| | Interrupt | GPIO9 | Touch interrupt |
| | Reset | GPIO13 | Touch reset |
| **I2C Bus (Shared)** | | | **Multi-device Bus** |
| | SDA | GPIO11 | I2C data line |
| | SCL | GPIO10 | I2C clock line |
| **Audio** | | | **PWM Buzzer** |
| | Buzzer | GPIO42 | Onboard buzzer |
| **Power Management** | | | **System Control** |
| | Power Enable | GPIO41 | SYS_EN - System power control |
| | Power Output | GPIO40 | SYS_OUT - Power status |
| **RTC (PCF85063)** | | | **Real-Time Clock** |
| | I2C Address | 0x51 | RTC chip address |
| | Interrupt | GPIO39 | RTC interrupt |
| **IMU (QMI8658C)** | | | **6-Axis Sensor** |
| | I2C Address | 0x6B | IMU chip address |
| **Buttons** | | | **User Input** |
| | PWM/BOOT Button | GPIO0 | Multi-function button |
| **External Sensors** | | | **Color Sensor** |
| | TCS3430 Interrupt | GPIO2 | Color sensor interrupt |
| | Status LED | GPIO18 | External RGB LED |
| **Available Pins** | | | **Expansion** |
| | GPIO17 | Free | Available for expansion |
| | GPIO3 | Free | Available for expansion |

## **I2C Device Map**

| **Device** | **Address** | **Function** | **Status** |
|------------|-------------|--------------|------------|
| CST816D | 0x15 | Touch Controller | ✅ Configured |
| PCF85063 | 0x51 | RTC Chip | ✅ Mapped |
| QMI8658C | 0x6B | 6-Axis IMU | ✅ Mapped |
| TCS3430 | 0x39 | Color Sensor (External) | ✅ Configured |

## **Implemented Functions**

### **Audio Feedback System**
```cpp
void init_buzzer();                              // Initialize buzzer
void buzzer_beep(uint16_t freq, uint16_t ms);   // Timed beep
void buzzer_tone(uint16_t frequency);           // Continuous tone
void buzzer_off();                              // Stop buzzer
```

**Audio Patterns:**
- **Boot Sequence**: 1000Hz (100ms) → 800Hz (200ms)
- **Color Match**: 1200Hz (150ms) - Exact match
- **Good Match**: 900Hz (100ms) - Within tolerance
- **No Audio**: No match or poor reading

### **Power Management System**
```cpp
void init_power_control();           // Initialize power pins
void system_power_enable(bool en);   // Control system power
bool get_system_power_status();      // Read power status
```

### **Visual Feedback System**
- **RGB LED Control**: Color-coded feedback based on measurements
- **Display Integration**: Real-time color swatch and data
- **Status Indicators**: Boot, error, and match status

## **Boot Sequence**

1. **Serial Initialization** (115200 baud)
2. **I2C Bus Setup** (GPIO11/GPIO10)
3. **Peripheral Initialization**:
   - RGB LED → Purple indicator
   - Buzzer → 1000Hz boot beep
   - Power management → System enable
4. **Display Setup**:
   - ST7789V2 initialization
   - LVGL configuration
   - Backlight activation
5. **Touch Controller Setup**:
   - CST816D initialization
   - Touch calibration
6. **UI Initialization**:
   - LVGL interface creation
   - Touch input registration
7. **Sensor Setup**:
   - TCS3430 detection and configuration
   - Interrupt setup (GPIO2)
   - Calibration system load
8. **Ready State**:
   - Success beep (800Hz, 200ms)
   - "Ready to measure!" message
   - LED off, system ready

## **Operation Modes**

### **Normal Operation**
- **Sensor Reading**: Every 1000ms
- **UI Updates**: Real-time color display
- **Audio Feedback**: Match-based beeps
- **LED Feedback**: Color-coded status

### **Calibration Mode**
- **Triggered**: Via UI "Calibrate" button
- **Process**: White reference measurement
- **Storage**: NVS persistent storage
- **Feedback**: Audio confirmation

### **Color Matching**
- **Algorithm**: CIE L*a*b* Delta E calculation
- **Tolerance**: 3.0 Delta E units
- **Database**: Dulux color reference
- **Feedback**: Visual + Audio + LED

## **Memory Usage**
- **RAM**: 55.0% (180,112 / 327,680 bytes)
- **Flash**: 17.6% (588,869 / 3,342,336 bytes)
- **PSRAM**: 8MB available for LVGL buffers

## **Power Consumption Optimization**
- **Display Backlight**: PWM controlled (GPIO15)
- **Sensor Sleep**: Auto-sleep after interrupt
- **System Power**: Controllable via GPIO41
- **Battery Support**: ETA6098 charging chip

## **Expansion Capabilities**

### **Available Resources**
- **GPIO17, GPIO3**: Free for additional sensors
- **I2C Bus**: Shared bus supports multiple devices
- **SPI Bus**: Available for additional displays/sensors
- **PSRAM**: 8MB for data buffering and processing

### **Potential Additions**
- **Wi-Fi Connectivity**: Remote monitoring
- **SD Card**: Data logging capability
- **Additional Sensors**: Environmental monitoring
- **Wireless Communication**: BLE data transmission

## **Testing Checklist**

### **Hardware Validation** ✅
- [x] Display initialization and rendering
- [x] Touch controller responsiveness
- [x] I2C bus communication
- [x] Buzzer audio output
- [x] Power management control
- [x] LED status indication

### **Software Validation** ✅
- [x] LVGL interface functionality
- [x] Color sensor readings
- [x] NVS calibration storage
- [x] Color matching algorithm
- [x] Audio feedback patterns
- [x] Error handling and recovery

### **Integration Testing** ✅
- [x] Complete boot sequence
- [x] Real-time color measurement
- [x] Touch interface interaction
- [x] Calibration procedure
- [x] Match detection and feedback
- [x] System stability and performance

## **Deployment Status: 🚀 PRODUCTION READY**

The ESP32-S3 ColorMatcher firmware is fully configured for the Waveshare ESP32-S3-Touch-LCD-1.69 board with complete peripheral integration, optimized performance, and comprehensive user feedback systems.
