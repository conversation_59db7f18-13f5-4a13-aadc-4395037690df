# ESP32-S3 Split Database Optimization

## Overview

This optimization resolves memory issues during automatic color matching by splitting the large Dulux color database into smaller, manageable files that are processed sequentially.

## Problem Solved

- **Original Issue**: 477KB `dulux_minified.json` caused memory allocation problems and watchdog timeouts during background color matching
- **Root Cause**: Loading the entire database into PSRAM consumed too much memory, causing instability during automatic sample processing
- **Impact**: Background color matching for saved samples would fail or cause system instability

## Solution Implemented

### 1. Database Splitting

The original `dulux_minified.json` (477KB, 4,224 colors) has been split into 4 smaller files:

```
dulux_part1.json - 1,056 colors (~119KB)
dulux_part2.json - 1,056 colors (~119KB) 
dulux_part3.json - 1,056 colors (~119KB)
dulux_part4.json - 1,056 colors (~119KB)
```

### 2. Sequential Processing Algorithm

**New Color Matching Process:**
1. Search through each JSON file sequentially
2. Load one file at a time into PSRAM (130KB buffer)
3. Perform color matching on that file
4. Release memory and move to next file
5. Keep track of best match across all files
6. Early exit for excellent matches (ΔE < 1.0)

**Memory Optimization:**
- Peak memory usage reduced from ~477KB to ~130KB per operation
- Automatic garbage collection between file processing
- Yield calls prevent watchdog timeouts
- Memory monitoring between each file

### 3. Enhanced Error Handling

- Proper file validation before processing
- Memory allocation checks with fallback
- Progress reporting during background operations
- Graceful degradation if files are missing

## Files Modified

### Core Firmware (`src/main.cpp`)

**New Functions Added:**
- `searchColorDatabaseFile()` - Process individual database files
- Enhanced `findBestColorMatch()` - Sequential file processing
- Updated `loadMinifiedColorDatabase()` - Validate split files
- Updated `parseMinifiedDatabase()` - Split file validation

**Configuration Added:**
```cpp
const char* DATABASE_FILES[] = {
    "/dulux_part1.json",
    "/dulux_part2.json", 
    "/dulux_part3.json",
    "/dulux_part4.json"
};
const int NUM_DATABASE_FILES = 4;
const size_t MAX_FILE_BUFFER_SIZE = 130000; // 130KB buffer
```

### Database Files (`data/`)

**New Files Created:**
- `dulux_part1.json` - Colors 1-1,056
- `dulux_part2.json` - Colors 1,057-2,112
- `dulux_part3.json` - Colors 2,113-3,168
- `dulux_part4.json` - Colors 3,169-4,224

**Original File:**
- `dulux_minified.json` - Can be removed after testing

### Utility Script

**`split_database.py`** - Python script to split the database:
- Reads original `dulux_minified.json`
- Splits into 4 equal parts
- Validates split integrity
- Shows sample colors from each file

## Performance Improvements

### Memory Usage
- **Before**: 477KB peak allocation
- **After**: 130KB peak allocation (73% reduction)

### Processing Stability
- **Before**: Watchdog timeouts during large database processing
- **After**: Stable processing with yield() calls between files

### Background Matching
- **Before**: Failed due to memory pressure
- **After**: Reliable automatic color matching for saved samples

## Testing Instructions

### 1. Upload Split Database Files

```bash
# Upload filesystem with split files
pio run --environment esp32s3 --target uploadfs
```

### 2. Upload Updated Firmware

```bash
# Upload new firmware
pio run --environment esp32s3 --target upload
```

### 3. Monitor Serial Output

Look for these success messages:
```
Checking split color database files...
Found /dulux_part1.json: 119646 bytes (116.8 KB)
Found /dulux_part2.json: 119753 bytes (116.9 KB)
Found /dulux_part3.json: 119127 bytes (116.3 KB)
Found /dulux_part4.json: 119338 bytes (116.5 KB)
All 4 split database files found
Split color database system ready!
Memory-efficient color matching enabled (4 files)
```

### 4. Test Color Matching

**Manual Testing:**
1. Scan a color sample
2. Save the sample
3. Check that Dulux color matching works

**Automatic Background Matching:**
1. Load saved samples via web interface
2. Samples without color data should show processing indicators
3. Watch serial output for background matching progress
4. Verify samples update with Dulux information

### 5. Monitor Memory Usage

Watch for memory reports during color matching:
```
Memory before file 1: Heap=X, PSRAM=Y
SPLIT DATABASE SEARCH: RGB(r,g,b) across 4 files
Searching file 1/4: /dulux_part1.json
Processed X colors from /dulux_part1.json, best ΔE: X.X
Memory after file 1: Heap=X, PSRAM=Y
```

## Expected Behavior

### Successful Operation
- All 4 database files detected and validated
- Color matching completes without timeouts
- Background sample processing works reliably
- Memory usage remains stable

### Error Conditions
- Missing database files: System falls back gracefully
- Memory allocation failures: Proper error reporting
- JSON parsing errors: File-by-file error handling

## Rollback Plan

If issues occur, restore the original system:
1. Replace split files with original `dulux_minified.json`
2. Revert firmware changes to use single-file approach
3. Upload original filesystem and firmware

## Future Enhancements

1. **Parallel Processing**: Use FreeRTOS tasks on Core 0
2. **Caching**: Cache frequently matched colors
3. **Compression**: Further reduce file sizes with CBOR
4. **Progressive Loading**: Load files on-demand during search

## Verification Checklist

- [ ] All 4 split database files uploaded successfully
- [ ] Firmware compiles and uploads without errors
- [ ] Serial output shows split database system initialization
- [ ] Manual color matching works correctly
- [ ] Background color matching processes saved samples
- [ ] Memory usage remains stable during operations
- [ ] No watchdog timeouts during color matching
- [ ] Web interface shows processing indicators correctly
