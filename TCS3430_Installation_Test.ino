/*!
 * @file TCS3430_Installation_Test.ino
 * @brief Installation verification test for TCS3430 sensor with calibration
 * @n This test verifies that the TCS3430 library is properly installed and working
 * @n Tests basic sensor communication, calibration functions, and color measurement
 * 
 * Hardware Connections for ESP32-S3 ProS3:
 * TCS3430 -> ESP32-S3 ProS3
 * VCC -> 3.3V
 * GND -> GND
 * SDA -> GPIO3 (or your I2C SDA pin)
 * SCL -> GPIO4 (or your I2C SCL pin)
 * INT -> GPIO1 (optional)
 */

#include <Wire.h>
#include <Adafruit_NeoPixel.h>
#include "DFRobot_TCS3430.h"

// Pin definitions for ESP32-S3 ProS3
#define I2C_SDA_PIN 3
#define I2C_SCL_PIN 4
#define RGB_LED_PIN 18

// RGB LED setup
Adafruit_NeoPixel rgbLED(1, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);

// TCS3430 sensor
DFRobot_TCS3430 colorSensor;

// Test status tracking
bool sensorInitialized = false;
bool basicTestPassed = false;
bool calibrationTestPassed = false;

void setup() {
  Serial.begin(115200);
  while (!Serial) delay(10);
  
  Serial.println("=== TCS3430 Installation Verification Test ===");
  Serial.println("Testing TCS3430 sensor library installation and functionality");
  Serial.println();
  
  // Initialize RGB LED
  rgbLED.begin();
  rgbLED.setBrightness(50);
  setStatusLED(255, 255, 0); // Yellow = testing
  
  // Initialize I2C
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
  Wire.setClock(400000); // 400kHz
  
  Serial.println("Step 1: Testing sensor initialization...");
  if (colorSensor.begin()) {
    Serial.println("✓ TCS3430 sensor initialized successfully!");
    sensorInitialized = true;
    setStatusLED(0, 255, 0); // Green = success
  } else {
    Serial.println("✗ Failed to initialize TCS3430 sensor!");
    Serial.println("  Check wiring and I2C connections");
    setStatusLED(255, 0, 0); // Red = error
    while(1) {
      delay(1000);
      Serial.println("  Please check connections and reset");
    }
  }
  
  delay(1000);
  
  Serial.println("\nStep 2: Testing basic sensor readings...");
  testBasicReadings();
  
  delay(2000);
  
  Serial.println("\nStep 3: Testing calibration functions...");
  testCalibrationFunctions();
  
  delay(1000);
  
  Serial.println("\n=== INSTALLATION TEST RESULTS ===");
  Serial.printf("Sensor Initialization: %s\n", sensorInitialized ? "PASS" : "FAIL");
  Serial.printf("Basic Readings Test: %s\n", basicTestPassed ? "PASS" : "FAIL");
  Serial.printf("Calibration Test: %s\n", calibrationTestPassed ? "PASS" : "FAIL");
  
  if (sensorInitialized && basicTestPassed && calibrationTestPassed) {
    Serial.println("\n🎉 ALL TESTS PASSED! TCS3430 installation is successful!");
    Serial.println("You can now use the full color measurement system.");
    setStatusLED(0, 255, 0); // Green = all good
  } else {
    Serial.println("\n⚠️  Some tests failed. Check the issues above.");
    setStatusLED(255, 165, 0); // Orange = partial success
  }
  
  Serial.println("\n=== STARTING CONTINUOUS MEASUREMENT ===");
  Serial.println("The sensor will now provide continuous color readings...");
}

void loop() {
  if (!sensorInitialized) {
    // Flash red if sensor not working
    setStatusLED(255, 0, 0);
    delay(500);
    setStatusLED(0, 0, 0);
    delay(500);
    return;
  }
  
  // Read sensor data
  uint16_t x = colorSensor.getXData();
  uint16_t y = colorSensor.getYData();
  uint16_t z = colorSensor.getZData();
  uint16_t ir1 = colorSensor.getIR1Data();
  
  // Convert to RGB for display
  float X = x / 65535.0f;
  float Y = y / 65535.0f;
  float Z = z / 65535.0f;
  
  uint8_t r, g, b;
  colorSensor.convertXYZtoRGB(X, Y, Z, &r, &g, &b);
  
  // Display readings
  Serial.printf("XYZ: %5d %5d %5d | RGB: %3d %3d %3d | IR1: %5d | Sat: %s\n",
                x, y, z, r, g, b, ir1, 
                colorSensor.isSaturated() ? "YES" : "NO");
  
  // Show color on LED
  setStatusLED(r, g, b);
  
  delay(1000);
}

void testBasicReadings() {
  // Configure sensor for testing
  colorSensor.setIntegrationTime(0x23);  // ~100ms
  colorSensor.setALSGain(1);  // 4x gain
  colorSensor.setAutoZeroMode(0);
  colorSensor.setAutoZeroNTHIteration(0x7F);
  
  delay(200); // Allow sensor to stabilize
  
  Serial.println("Taking 5 test readings...");
  bool hasValidReadings = false;
  
  for (int i = 0; i < 5; i++) {
    uint16_t x = colorSensor.getXData();
    uint16_t y = colorSensor.getYData();
    uint16_t z = colorSensor.getZData();
    uint16_t ir1 = colorSensor.getIR1Data();
    
    Serial.printf("  Reading %d: X=%d, Y=%d, Z=%d, IR1=%d\n", i+1, x, y, z, ir1);
    
    // Check if we have reasonable readings (not all zeros)
    if (x > 0 || y > 0 || z > 0) {
      hasValidReadings = true;
    }
    
    delay(150);
  }
  
  if (hasValidReadings) {
    Serial.println("✓ Basic sensor readings test PASSED");
    basicTestPassed = true;
  } else {
    Serial.println("✗ Basic sensor readings test FAILED - all readings are zero");
    Serial.println("  Check sensor power and lighting conditions");
    basicTestPassed = false;
  }
}

void testCalibrationFunctions() {
  Serial.println("Testing calibration function availability...");
  
  // Test that calibration functions exist and can be called
  try {
    // Test getCalibratedXYZ function
    float x, y, z;
    bool hasCalibration = colorSensor.getCalibratedXYZ(&x, &y, &z);
    Serial.printf("✓ getCalibratedXYZ() function works (calibrated: %s)\n", 
                  hasCalibration ? "YES" : "NO");
    
    // Test XYZ to RGB conversion
    uint8_t r, g, b;
    colorSensor.convertXYZtoRGB(0.5f, 0.5f, 0.5f, &r, &g, &b);
    Serial.printf("✓ convertXYZtoRGB() function works (test RGB: %d,%d,%d)\n", r, g, b);
    
    // Test saturation check
    bool saturated = colorSensor.isSaturated();
    Serial.printf("✓ isSaturated() function works (currently: %s)\n", 
                  saturated ? "YES" : "NO");
    
    // Test optimal gain function
    uint8_t optimalGain = colorSensor.getOptimalGain();
    Serial.printf("✓ getOptimalGain() function works (recommended: %dx)\n", 
                  1 << (optimalGain * 2));
    
    // Test integration time function
    float integrationMs = colorSensor.getIntegrationTimeMs();
    Serial.printf("✓ getIntegrationTimeMs() function works (current: %.1fms)\n", 
                  integrationMs);
    
    Serial.println("✓ All calibration functions are available and working");
    calibrationTestPassed = true;
    
  } catch (...) {
    Serial.println("✗ Calibration function test FAILED - functions not available");
    calibrationTestPassed = false;
  }
  
  Serial.println("\nNOTE: To perform actual calibration:");
  Serial.println("  1. Use performWhiteBalanceCalibration() with white reference");
  Serial.println("  2. Use performDarkCalibration() in complete darkness");
  Serial.println("  3. These require user interaction and are not tested automatically");
}

void setStatusLED(uint8_t r, uint8_t g, uint8_t b) {
  rgbLED.setPixelColor(0, rgbLED.Color(r, g, b));
  rgbLED.show();
}
