# ESP32-S3-Touch-LCD-1.69 Board Configuration

## Board Overview
**Waveshare ESP32-S3-Touch-LCD-1.69** - Low-cost, high-performance MCU board with integrated peripherals.

### Key Specifications
- **MCU**: ESP32-S3R8 (Xtensa® 32-bit LX7 dual-core, up to 240MHz)
- **Memory**: 512KB SRAM, 384KB ROM, 8MB PSRAM, 16MB Flash (W250128JV-SIQ)
- **Connectivity**: 2.4GHz Wi-Fi (802.11 b/g/n), Bluetooth® 5 (BLE)
- **Display**: 1.69" capacitive LCD, 240×280 resolution, 262K colors, ST7789V2 controller
- **Touch**: CST816D capacitive touch controller

## Pin Configuration (Updated for Board)

### Display (ST7789V2) - SPI Interface
```cpp
#define TFT_SPI_HOST       SPI2_HOST
#define TFT_MOSI_PIN       6       // GPIO6 - SDA (MOSI)
#define TFT_SCLK_PIN       7       // GPIO7 - SCL (Clock)
#define TFT_CS_PIN         5       // GPIO5 - CSX (Chip Select)
#define TFT_DC_PIN         4       // GPIO4 - D/CX (Data/Command)
#define TFT_RST_PIN        8       // GPIO8 - RESX (Reset)
#define TFT_BL_PIN         15      // GPIO15 - Backlight control
```

### Touch Controller (CST816D) - I2C Interface
```cpp
#define TOUCH_I2C_ADDRESS  0x15    // 7-bit address (write: 0x2A, read: 0x2B)
#define TOUCH_INT_PIN      9       // GPIO9 - Touch interrupt
#define TOUCH_RST_PIN      13      // GPIO13 - Touch reset
```

### I2C Bus (Shared for multiple peripherals)
```cpp
#define I2C_SDA_PIN        11      // GPIO11 - SDA
#define I2C_SCL_PIN        10      // GPIO10 - SCL
```

### Onboard Peripherals
```cpp
// RTC (PCF85063)
#define RTC_I2C_ADDRESS    0x51
#define RTC_INT_PIN        39      // GPIO39 - RTC interrupt

// IMU (QMI8658C) - 6-axis (3-axis gyro + 3-axis accel)
#define IMU_I2C_ADDRESS    0x6B

// Audio
#define BUZZER_PIN         42      // GPIO42 - Onboard buzzer

// Buttons
#define PWM_BUTTON_PIN     0       // GPIO0 - Multi-function button
#define BOOT_BUTTON_PIN    0       // GPIO0 - BOOT button (same as PWM)

// Power Management
#define SYS_EN_PIN         41      // GPIO41 - System power enable control
#define SYS_OUT_PIN        40      // GPIO40 - System power output control
```

### Available GPIO for External Sensors
```cpp
// External TCS3430 Color Sensor
#define SENSOR_INTERRUPT_PIN    2  // GPIO2 - TCS3430 interrupt
#define RGB_LED_PIN            18  // GPIO18 - Status LED

// Additional available pins from pinout
// GPIO17, GPIO18, GPIO3, GPIO2 (partially used)
```

## Hardware Features

### Built-in Components
1. **Display**: 1.69" ST7789V2 LCD (240×280, RGB565)
2. **Touch**: CST816D capacitive touch
3. **RTC**: PCF85063 with battery backup
4. **IMU**: QMI8658C 6-axis sensor
5. **Audio**: Onboard buzzer
6. **Power**: ETA6098 Li-ion charging chip
7. **Connectivity**: Wi-Fi + Bluetooth with onboard antenna

### Power Management
- **Battery**: MX1.25 connector for 3.7V Li-ion battery
- **Charging**: ETA6098 high-efficiency charging manager
- **USB**: Type-C connector for programming and power

### Communication Interfaces
- **SPI**: 4-wire SPI for display (ST7789V2)
- **I2C**: Shared bus for touch, RTC, IMU, and external sensors
- **UART**: U0TXD/U0RXD for programming and debugging

## Display Specifications

### ST7789V2 Controller
- **Resolution**: 240(H) × 280(V) pixels
- **Colors**: 262K colors (RGB565 format)
- **Interface**: 4-wire SPI
- **Features**: Round corners, toughened glass + film material

### SPI Timing
- **Mode**: SPI Mode 0 (CPOL=0, CPHA=0)
- **Speed**: Up to 80MHz (typical 40MHz for stability)
- **Data**: 8-bit transmission, MSB first

## Touch Controller Specifications

### CST816D Features
- **Type**: Self-capacitance touch chip
- **Interface**: I2C (10KHz ~ 400KHz configurable)
- **Address**: 0x15 (7-bit), Write: 0x2A, Read: 0x2B
- **Features**: Single touch point, gesture recognition

## I2C Device Map
| Device | Address | Description |
|--------|---------|-------------|
| CST816D | 0x15 | Touch controller |
| PCF85063 | 0x51 | RTC chip |
| QMI8658C | 0x6B | 6-axis IMU |
| TCS3430 | 0x39 | Color sensor (external) |

## Memory Configuration
- **SRAM**: 512KB internal
- **ROM**: 384KB internal  
- **PSRAM**: 8MB external (enabled in build flags)
- **Flash**: 16MB external NOR flash

## Build Configuration Updates
The project has been updated with board-specific configurations:

1. **Pin mappings** updated for ESP32-S3-Touch-LCD-1.69
2. **PSRAM support** enabled (8MB available)
3. **Display controller** configured for ST7789V2
4. **Touch controller** configured for CST816D
5. **I2C bus** shared between multiple peripherals

## Usage Notes
1. **External TCS3430**: Connect to I2C bus (SDA=GPIO11, SCL=GPIO10)
2. **Power**: Can run on battery or USB power
3. **Programming**: Use USB Type-C connector
4. **Reset**: Use RST button or GPIO reset
5. **Boot mode**: Hold BOOT button during reset for download mode

## Compatibility
- Fully compatible with existing LVGL 8.3.11 configuration
- All previous firmware features preserved
- Enhanced with board-specific optimizations
