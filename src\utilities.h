#ifndef UTILITIES_H
#define UTILITIES_H

#include <Arduino.h>

// Logging levels
#define LOG_LEVEL_ERROR   1
#define LOG_LEVEL_WARN    2
#define LOG_LEVEL_INFO    3
#define LOG_LEVEL_DEBUG   4
#define LOG_LEVEL_VERBOSE 5

// Current log level (can be adjusted based on build configuration)
#ifndef CURRENT_LOG_LEVEL
#define CURRENT_LOG_LEVEL LOG_LEVEL_INFO
#endif

// Utility macro for stringifying defines
#define STRINGIFY(x) #x

// Logging function declarations
void log_message(int level, const char* component_tag, const char* message);

// Convenience macros for different log levels
#define log_error(tag, msg)   log_message(LOG_LEVEL_ERROR, tag, msg)
#define log_warning(tag, msg) log_message(LOG_LEVEL_WARN, tag, msg)
#define log_info(tag, msg)    log_message(LOG_LEVEL_INFO, tag, msg)
#define log_debug(tag, msg)   log_message(LOG_LEVEL_DEBUG, tag, msg)
#define log_verbose(tag, msg) log_message(LOG_LEVEL_VERBOSE, tag, msg)

// RGB LED control functions
void init_rgb_led();
void set_rgb_led_color(uint8_t r, uint8_t g, uint8_t b);

// ESP32-S3-Touch-LCD-1.69 board-specific functions
void init_buzzer();
void buzzer_beep(uint16_t frequency, uint16_t duration_ms);
void buzzer_tone(uint16_t frequency);
void buzzer_off();

// Power management functions
void init_power_control();
void system_power_enable(bool enable);
bool get_system_power_status();

#endif // UTILITIES_H
