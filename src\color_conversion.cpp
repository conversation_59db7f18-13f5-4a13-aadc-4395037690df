#include "color_conversion.h"

// Standard D65 White Point reference values (Y = 100)
const xyz_color_s D65_WHITEPOINT = {95.047f, 100.0f, 108.883f};

// sRGB (D65) to XYZ conversion matrix constants
const float MATRIX_SRGB_TO_XYZ[3][3] = {
    {0.4124564f, 0.3575761f, 0.1804375f},
    {0.2126729f, 0.7151522f, 0.0721750f},
    {0.0193339f, 0.1191920f, 0.9503041f}
};

// XYZ (D65) to sRGB conversion matrix constants
const float MATRIX_XYZ_TO_SRGB[3][3] = {
    { 3.2404542f, -1.5371385f, -0.4985314f},
    {-0.9692660f,  1.8760108f,  0.0415560f},
    { 0.0556434f, -0.2040259f,  1.0572252f}
};

// sRGB gamma correction (linear RGB 0-1 to sRGB 0-1)
float srgb_gamma_correct(float linear_val) {
    if (linear_val <= 0.0f) return 0.0f;
    if (linear_val >= 1.0f) return 1.0f; // Clamp high
    if (linear_val <= 0.0031308f) {
        return 12.92f * linear_val;
    } else {
        return 1.055f * powf(linear_val, 1.0f / 2.4f) - 0.055f;
    }
}

// sRGB inverse gamma correction (sRGB 0-1 to linear RGB 0-1)
float srgb_inverse_gamma_correct(float srgb_val) {
    if (srgb_val <= 0.0f) return 0.0f;
    if (srgb_val >= 1.0f) return 1.0f; // Clamp high
    if (srgb_val <= 0.04045f) {
        return srgb_val / 12.92f;
    } else {
        return powf((srgb_val + 0.055f) / 1.055f, 2.4f);
    }
}

rgb_color_s xyz_to_display_srgb(const xyz_color_s &xyz_sensor_values, float normalization_factor) {
    rgb_color_s rgb_out;
    // Ensure normalization_factor is positive to avoid division by zero or negative results.
    if (normalization_factor <= 1.0f) normalization_factor = 65535.0f; // Default if invalid (e.g. max 16-bit sensor value)

    // Normalize sensor XYZ values. This assumes xyz_sensor_values are somewhat proportional to standard XYZ.
    float X_norm = xyz_sensor_values.x / normalization_factor;
    float Y_norm = xyz_sensor_values.y / normalization_factor;
    float Z_norm = xyz_sensor_values.z / normalization_factor;

    // Transform normalized XYZ to linear sRGB (0-1 range)
    float R_linear = MATRIX_XYZ_TO_SRGB[0][0] * X_norm + MATRIX_XYZ_TO_SRGB[0][1] * Y_norm + MATRIX_XYZ_TO_SRGB[0][2] * Z_norm;
    float G_linear = MATRIX_XYZ_TO_SRGB[1][0] * X_norm + MATRIX_XYZ_TO_SRGB[1][1] * Y_norm + MATRIX_XYZ_TO_SRGB[1][2] * Z_norm;
    float B_linear = MATRIX_XYZ_TO_SRGB[2][0] * X_norm + MATRIX_XYZ_TO_SRGB[2][1] * Y_norm + MATRIX_XYZ_TO_SRGB[2][2] * Z_norm;

    // Apply sRGB gamma correction
    float R_srgb_norm = srgb_gamma_correct(R_linear);
    float G_srgb_norm = srgb_gamma_correct(G_linear);
    float B_srgb_norm = srgb_gamma_correct(B_linear);

    // Convert to 0-255 range and clamp
    rgb_out.r = (uint8_t)constrain(R_srgb_norm * 255.0f, 0.0f, 255.0f);
    rgb_out.g = (uint8_t)constrain(G_srgb_norm * 255.0f, 0.0f, 255.0f);
    rgb_out.b = (uint8_t)constrain(B_srgb_norm * 255.0f, 0.0f, 255.0f);
    return rgb_out;
}

// Helper function for L*a*b* conversion (CIE 1976 formula)
static float f_lab_transform(float t) {
    const float delta = 6.0f / 29.0f;
    if (t > (delta * delta * delta)) { // t > (6/29)^3 which is approx 0.008856
        return powf(t, 1.0f / 3.0f);
    } else {
        return (t / (3.0f * delta * delta)) + (4.0f / 29.0f);
        // (t / ( (29/6)^-2 * 1/3 ) ) + 4/29  = (t * (29^2 / (3*6^2))) + 4/29
        // (t * 24389/27 / 116) * 116/16  = (kappa*t + 16)/116 where kappa = 24389/27
    }
}

void xyz_to_lab(const xyz_color_s &xyz_in, lab_color_s &lab_out, const xyz_color_s &white_point_ref) {
    // Normalize input XYZ relative to the specified white point
    float xr = xyz_in.x / white_point_ref.x;
    float yr = xyz_in.y / white_point_ref.y;
    float zr = xyz_in.z / white_point_ref.z;

    float fx = f_lab_transform(xr);
    float fy = f_lab_transform(yr);
    float fz = f_lab_transform(zr);

    lab_out.l = 116.0f * fy - 16.0f;
    lab_out.a = 500.0f * (fx - fy);
    lab_out.b = 200.0f * (fy - fz); // Standard CIELAB definition
}

xyz_color_s srgb_to_xyz(const rgb_color_s &srgb_color) {
    // Normalize 0-255 sRGB components to 0-1 range
    float r_norm = srgb_color.r / 255.0f;
    float g_norm = srgb_color.g / 255.0f;
    float b_norm = srgb_color.b / 255.0f;

    // Convert to linear RGB
    float r_linear = srgb_inverse_gamma_correct(r_norm);
    float g_linear = srgb_inverse_gamma_correct(g_norm);
    float b_linear = srgb_inverse_gamma_correct(b_norm);

    xyz_color_s xyz_out;
    // Apply sRGB to XYZ conversion matrix and scale by 100 (so Y of white D65 is 100)
    xyz_out.x = (MATRIX_SRGB_TO_XYZ[0][0] * r_linear + MATRIX_SRGB_TO_XYZ[0][1] * g_linear + MATRIX_SRGB_TO_XYZ[0][2] * b_linear) * 100.0f;
    xyz_out.y = (MATRIX_SRGB_TO_XYZ[1][0] * r_linear + MATRIX_SRGB_TO_XYZ[1][1] * g_linear + MATRIX_SRGB_TO_XYZ[1][2] * b_linear) * 100.0f;
    xyz_out.z = (MATRIX_SRGB_TO_XYZ[2][0] * r_linear + MATRIX_SRGB_TO_XYZ[2][1] * g_linear + MATRIX_SRGB_TO_XYZ[2][2] * b_linear) * 100.0f;
    return xyz_out;
}

float delta_e_cie76(const lab_color_s &lab1, const lab_color_s &lab2) {
    float dL = lab1.l - lab2.l;
    float da = lab1.a - lab2.a;
    float db = lab1.b - lab2.b;
    return sqrtf(dL * dL + da * da + db * db);
}

void apply_ir_compensation(xyz_color_s &xyz_raw_data, uint16_t ir1_raw, uint16_t ir2_raw, float k_ir_comp_factor) {
    // Enhanced IR compensation model for TCS3430 sensor
    // The TCS3430 has two IR channels (IR1 and IR2) that can be used to estimate
    // and compensate for infrared contamination in the XYZ channels.

    // Validate inputs
    if (k_ir_comp_factor < 0.0f || k_ir_comp_factor > 1.0f) {
        k_ir_comp_factor = 0.05f; // Default safe value
    }

    // Calculate average IR contribution for more stable compensation
    float avg_ir = (float)(ir1_raw + ir2_raw) / 2.0f;

    // Apply different compensation factors for each channel based on typical
    // TCS3430 spectral response characteristics
    float ir_contribution_x = k_ir_comp_factor * avg_ir * 0.8f; // X channel less affected
    float ir_contribution_y = k_ir_comp_factor * avg_ir * 1.0f; // Y channel most affected
    float ir_contribution_z = k_ir_comp_factor * avg_ir * 0.6f; // Z channel moderately affected

    // Apply compensation with bounds checking
    xyz_raw_data.x = fmax(0.0f, xyz_raw_data.x - ir_contribution_x);
    xyz_raw_data.y = fmax(0.0f, xyz_raw_data.y - ir_contribution_y);
    xyz_raw_data.z = fmax(0.0f, xyz_raw_data.z - ir_contribution_z);

    // Additional validation to prevent negative values
    if (xyz_raw_data.x < 0.0f) xyz_raw_data.x = 0.0f;
    if (xyz_raw_data.y < 0.0f) xyz_raw_data.y = 0.0f;
    if (xyz_raw_data.z < 0.0f) xyz_raw_data.z = 0.0f;
}